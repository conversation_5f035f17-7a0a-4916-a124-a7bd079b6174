import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getDatabase } from 'firebase/database';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

const firebaseConfig = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  projectId: process.env.FIREBASE_PROJECT_ID,
  storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const realtimeDb = getDatabase(app);
export const messaging = getMessaging(app);

export const requestNotificationPermission = async () => {
  try {
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      return true;
    } else if (permission === 'denied') {
      return false;
    } else {
      return false;
    }
  } catch (error) {
    return false;
  }
};

export const getMessagingToken = async () => {
  let currentToken = '';
  if (!messaging) return;

  const hasPermission = await requestNotificationPermission();
  if (!hasPermission) {
    return '';
  }

  try {
    currentToken = await getToken(messaging, {
      vapidKey: process.env.FIREBASE_VAPID_KEY,
    });
  } catch (error) {
    console.log('An error occurred while retrieving token. ', error);
  }
  return currentToken;
};

export const onMessageListener = () =>
  new Promise(resolve => {
    onMessage(messaging, payload => {
      resolve(payload);
    });
  });
