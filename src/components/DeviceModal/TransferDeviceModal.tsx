import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Flex, Modal } from 'antd';
import { Trans } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import type { DeviceType } from 'types/device';

import images from 'assets/images';
import { BodyMdExtend, FontSemibold } from 'assets/styles';
import { Icon } from 'components';
import AccountDestinationModal from 'components/AccountDestinationModal';
import { PrimaryButton } from 'components/Button';

interface TransferDeviceModalProps {
  isLoading: boolean;
  visible: boolean;
  multipleDeviceSelected: DeviceType[];
  onClose?: () => void;
  onSubmit: (userIds: string[], parentId: string) => void;
  onSelectedMultipleDevice?: (keys: DeviceType[]) => void;
}

const TransferDeviceModal: React.FC<TransferDeviceModalProps> = ({
  isLoading,
  visible,
  onClose,
  onSubmit,
  onSelectedMultipleDevice,
  multipleDeviceSelected = [],
}) => {
  const [showDestinationModal, setShowDestinationModal] = useState(false);
  const [destinationAccount, setDestinationAccount] = useState<
    DeviceType | null | undefined
  >(null);

  const isButtonDisable = useMemo(() => {
    return !destinationAccount || multipleDeviceSelected.length === 0;
  }, [destinationAccount, multipleDeviceSelected]);

  const handleSelectDestinationModal = (selectedItem: DeviceType | null) => {
    if (selectedItem) {
      setDestinationAccount(selectedItem);
      setShowDestinationModal(false);
    }
  };

  const handleSubmit = useCallback(() => {
    const imeis = multipleDeviceSelected.map(
      item => item?.imei?.toString(),
    ) as string[];
    const accountId = destinationAccount?.id?.toString() ?? '';
    if (imeis.length > 0 && accountId) {
      onSubmit(imeis, accountId);
    }
  }, [onSubmit, destinationAccount, multipleDeviceSelected]);

  const handleRemoveDevice = useCallback(
    (imei?: string) => () => {
      const filtered = multipleDeviceSelected.filter(
        item => item.imei && item.imei.toString() !== imei?.toString(),
      );
      onSelectedMultipleDevice?.(filtered);
    },
    [multipleDeviceSelected],
  );

  const handleResetState = useCallback(() => {
    setShowDestinationModal(false);
    setDestinationAccount(null);
  }, []);

  const toggleDestinationModal = useCallback(
    (status: boolean) => () => {
      setShowDestinationModal(status);
    },
    [],
  );

  const handleClose = useCallback(() => {
    handleResetState();
    setTimeout(() => {
      onClose?.();
    }, 0);
  }, [onClose]);

  useEffect(() => {
    if (!visible) {
      handleResetState();
    }
  }, [visible]);

  return (
    <>
      <Modal
        centered
        width={480}
        title={<Title>{t('business.titleMovingDevice')}</Title>}
        open={visible}
        onCancel={handleClose}
        onOk={handleClose}
        footer={null}
        closeIcon={
          <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
        }
      >
        <Body>
          <Row>
            <Label className={`${BodyMdExtend} text-gray-600`}>
              <Trans
                i18nKey='business.selectedDevice'
                values={{ count: multipleDeviceSelected.length }}
              />
            </Label>
            <div className='w-full overflow-auto pb-1'>
              <ListDeviceWrapper>
                {multipleDeviceSelected.map((item, index) => (
                  <DeviceItemWrapper key={index}>
                    <div className='flex max-w-[160px] flex-col'>
                      <DeviceItemHeader>{item?.device_name}</DeviceItemHeader>
                      <p className='truncate-1-line w-full whitespace-nowrap text-xs text-grey-600'>
                        {item.device_category} - {item.device_plate_number}
                      </p>
                    </div>
                    {multipleDeviceSelected.length > 1 && (
                      <RemoveButton
                        onClick={handleRemoveDevice(item?.imei?.toString())}
                      >
                        <Icon src={images.Icon.RadiusRemove} />
                      </RemoveButton>
                    )}
                  </DeviceItemWrapper>
                ))}
              </ListDeviceWrapper>
            </div>
          </Row>
          <Row>
            <Label className={`${BodyMdExtend} ${FontSemibold}`}>
              {t('business.destinationAccount')}
            </Label>
            <AgencyDeviceContainer
              className='bg-grey-50 '
              onClick={toggleDestinationModal(true)}
            >
              <Flex
                className='w-full'
                flex={1}
                justify='space-between'
                align='center'
              >
                <SelectionDevice>
                  {destinationAccount?.name ??
                    t('business.selectDestinationAccount')}
                </SelectionDevice>
                <Icon
                  src={images.Icon.CaretDownSm}
                  className={`size-5 rotate-0 transition-transform duration-200 ease-in-out ${
                    showDestinationModal ? '-rotate-180' : ''
                  }`}
                />
              </Flex>
            </AgencyDeviceContainer>
          </Row>
          <PrimaryButton
            htmlType='submit'
            disabled={isLoading || isButtonDisable}
            loading={isLoading}
            onClick={handleSubmit}
            className='mt-3'
          >
            {t('business.transfer')}
          </PrimaryButton>
        </Body>
      </Modal>
      {showDestinationModal && (
        <AccountDestinationModal
          expandUserRole={['distributor', 'end_user']}
          open={showDestinationModal}
          selectedId={destinationAccount?.id?.toString() ?? ''}
          onSelectItem={handleSelectDestinationModal}
          onClose={toggleDestinationModal(false)}
        />
      )}
    </>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const Body = tw.div`flex flex-col gap-3 py-1`;
const Row = tw.div`flex flex-col gap-1`;
const Label = tw.label`block text-left text-black-1000`;
const AgencyDeviceContainer = tw.div`w-full rounded-xl flex flex-row items-center justify-start py-2 px-3 gap-2 text-left text-sm text-text-secondary font-medium cursor-pointer`;
const SelectionDevice = tw.div`text-sm text-grey-600 leading-[24px] overflow-hidden text-ellipsis whitespace-nowrap truncate-1-line`;
const RemoveButton = tw.button`flex size-5 min-h-[20px] min-w-[20px] items-center justify-center rounded-[1000px] bg-red-10 p-[2px]`;
const ListDeviceWrapper = tw.div`flex w-fit min-w-full flex-row flex-nowrap gap-3 overflow-auto`;
const DeviceItemWrapper = tw.div`flex w-fit flex-auto items-center justify-between gap-3 overflow-auto rounded-2xl border border-grey-100 p-3`;
const DeviceItemHeader = tw.p`whitespace-nowrap text-sm font-bold text-black-1000`;

export default TransferDeviceModal;
