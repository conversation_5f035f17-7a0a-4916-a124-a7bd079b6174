import React, { useEffect, useMemo, useRef, useState } from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMarker } from 'react-map4d-map';
import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import { Colors } from 'assets/styles';

import { getStrCarMarker } from './carMarker';

type RRMarkerProps = {
  status: DeviceStatus;
  label: string;
  position: ILatLng;
  labelAnchor?: [number, number];
  rotation?: number;
  isAnimating?: boolean;
};

const RRMarker: React.FC<RRMarkerProps> = ({
  status = DeviceStatus.Running,
  position,
  rotation = 90,
  label = '',
  isAnimating = false,
}) => {
  const [smoothRotation, setSmoothRotation] = useState(rotation);
  const animationRef = useRef<number | null>(null);
  const targetRotationRef = useRef(rotation);
  const currentRotationRef = useRef(rotation);

  // Smooth rotation with requestAnimationFrame
  useEffect(() => {
    targetRotationRef.current = rotation;

    if (!isAnimating) {
      setSmoothRotation(rotation);
      currentRotationRef.current = rotation;
      return;
    }

    const animate = () => {
      const current = currentRotationRef.current;
      const target = targetRotationRef.current;

      let diff = target - current;
      if (diff > 180) diff -= 360;
      if (diff < -180) diff += 360;

      if (Math.abs(diff) < 0.1) {
        setSmoothRotation(target);
        currentRotationRef.current = target;
        return;
      }

      const newRotation = current + diff * 0.08;
      const normalizedRotation = (newRotation + 360) % 360;

      setSmoothRotation(normalizedRotation);
      currentRotationRef.current = normalizedRotation;

      animationRef.current = requestAnimationFrame(animate);
    };

    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [rotation, isAnimating]);

  const carIconView = useMemo(() => {
    return `
    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;transform: rotate(${rotation}deg); position: relative;">
      <div style="width: 48px; height: 29px; display: flex; align-items: center; justify-content: center;">
      ${getStrCarMarker(status)}
      </div>
      <div style="background-color: ${
        Colors.brand[100]
      }; padding: 4px; border-radius: 8px; font-size: 12px; font-weight: bold; box-shadow: 0 2px 4px rgba(0,0,0,0.1); white-space: nowrap; z-index: 10; margin-top: 4px;">${label}</div>
      </div>
    `;
  }, [status, rotation]);

  return (
    <>
      {/* Car icon using iconView (safer than icon URL) */}
      <MFMarker
        iconView={carIconView}
        rotation={0} // rotation handled in SVG
        position={position}
        anchor={[0.5, 0.5]}
      />
    </>
  );
};

export default RRMarker;
