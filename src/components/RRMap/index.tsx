import { useEffect, useMemo, useRef, useState } from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMap, MFPolyline } from 'react-map4d-map';

import DeviceMapActions from 'components/DeviceHistory/DeviceMapActions';

import PlayBlack from './PlayBack';
import RRCircle from './RRCircle';
import RRMaker from './RRMaker';
import SelectedDeviceMarker from './SelectedDeviceMarker';
import {
  NormalizedLatLng,
  calculateInterpolatedPosition,
  calculateRotationFromMovement,
  getCurrentPoint,
  normalizeLatLng,
} from './utils/mapUtils';

const RRMap: React.FC<any> = ({
  selectedPreviewDate,
  deviceList = [],
  selectedDevice,
  radius,
  path,
  currentViewData,
  currentDeviceItemAction,
  onItemActionClick,
}) => {
  const [sliderValue, setSliderValue] = useState(0);
  const previousSliderRef = useRef(0);
  const [pos, setPos] = useState<{ lat: number; lng: number } | undefined>(
    undefined,
  );
  const [smoothedBearing, setSmoothedBearing] = useState(0);
  const [playSpeed, setPlaySpeed] = useState(1);

  const currentMarkerPosition = useMemo((): NormalizedLatLng | null => {
    if (!path || path.length === 0) {
      return selectedDevice
        ? {
            lat: selectedDevice?.gps?.latitude || 0,
            lng: selectedDevice?.gps?.longitude || 0,
          }
        : null;
    }

    if (selectedPreviewDate && path.length > 1) {
      return calculateInterpolatedPosition(path, sliderValue);
    }

    if (path[0]) {
      return normalizeLatLng(path[0]);
    }

    return {
      lat: selectedDevice?.gps?.latitude || 0,
      lng: selectedDevice?.gps?.longitude || 0,
    };
  }, [path, sliderValue, selectedPreviewDate, selectedDevice]);

  // Separate memoization for status to prevent unnecessary re-renders
  const currentPoint = useMemo(() => {
    if (!selectedPreviewDate || !path || path.length === 0) {
      return undefined;
    }
    return getCurrentPoint(path, sliderValue);
  }, [path, sliderValue, selectedPreviewDate, selectedDevice?.status]);

  const carRotation = useMemo(() => {
    if (!path || path.length < 2 || !selectedPreviewDate) return 0;

    const result = calculateRotationFromMovement(
      path,
      sliderValue,
      previousSliderRef.current,
    );
    previousSliderRef.current = sliderValue; // Update after calculation
    return result;
  }, [path, sliderValue, selectedPreviewDate]);

  useEffect(() => {
    // Sync rotation immediately with position for smooth movement
    setSmoothedBearing(carRotation);
  }, [carRotation]);

  useEffect(() => {
    const newPos = selectedDevice
      ? {
          lat: selectedDevice?.gps?.latitude || 10.777478232010438,
          lng: selectedDevice?.gps?.longitude || 106.65874045021208,
        }
      : { lat: 10.777478232010438, lng: 106.65874045021208 };
    setPos(newPos);
  }, [selectedDevice]);

  useEffect(() => {
    if (selectedPreviewDate && path && path.length > 0) {
      setSliderValue(0);
      previousSliderRef.current = 0; // Reset previous slider tracking
    }
  }, [path, selectedPreviewDate]);

  useEffect(() => {
    if (selectedPreviewDate && currentMarkerPosition && sliderValue > 0) {
      const timeoutId = setTimeout(() => {
        setPos({
          lat: currentMarkerPosition.lat,
          lng: currentMarkerPosition.lng,
        });
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [currentMarkerPosition, selectedPreviewDate, sliderValue]);

  if (!pos || !pos.lat || !pos.lng) return null;

  return (
    <>
      <div style={{ width: '100%', height: '100%' }}>
        <MFMap
          options={{
            ...(pos ? { center: pos } : {}),
            controls: true,
          }}
          zoom={15}
          accessKey={process.env.ACCESS_KEY ?? ''}
          version={'2.3'}
        >
          {path?.length === 0 && (
            <MFPolyline
              strokeColor={'#2F6BFF'}
              strokeOpacity={0}
              strokeWidth={6}
              path={[]}
            />
          )}
          <SelectedDeviceMarker
            selectedDevice={selectedDevice}
            currentMarkerPosition={currentMarkerPosition}
            currentMarkerStatus={currentPoint?.status || selectedDevice?.status}
            currentViewData={currentViewData}
            path={path}
            smoothedBearing={smoothedBearing}
            selectedPreviewDate={selectedPreviewDate}
          />
          {!selectedDevice &&
            (deviceList || [])
              .filter(item => item.gps?.latitude && item.gps?.longitude)
              .map(item => (
                <RRMaker
                  key={item.id}
                  label={item?.device_plate_number || ''}
                  labelAnchor={[0.5, 2]}
                  rotation={item.gps.half_of_course || 0}
                  position={{
                    lat: item.gps?.latitude || 0,
                    lng: item.gps?.longitude || 0,
                  }}
                  status={item.status || DeviceStatus.Running}
                />
              ))}
          {!!radius && (
            <RRCircle
              radius={radius * 1000}
              fillOpacity={0.4}
              strokeWidth={1}
              center={pos}
            />
          )}
        </MFMap>
      </div>
      {selectedPreviewDate && (
        <PlayBlack
          max={100}
          value={sliderValue}
          onChange={setSliderValue}
          pathLength={path?.length || 0}
          playSpeed={playSpeed}
          currentPoint={currentPoint}
          device={selectedDevice}
          onPlaySpeedChange={setPlaySpeed}
          className='absolute inset-x-[200px] bottom-0 z-30 mx-auto w-full max-w-[30%] bg-white-1000'
        />
      )}
      {selectedDevice && (
        <div className='absolute bottom-0 right-3 top-3 z-20 flex h-fit w-[360px] flex-col'>
          <DeviceMapActions
            isRealTime={selectedPreviewDate}
            currentPoint={currentPoint}
            device={selectedDevice}
            onItemActionClick={onItemActionClick}
            selectedItem={currentDeviceItemAction}
          />
        </div>
      )}
    </>
  );
};

export default RRMap;
