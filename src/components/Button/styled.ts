import { LoadingOutlined } from '@ant-design/icons';
import tw from 'tailwind-styled-components';

export const Spinner = tw(LoadingOutlined)`animate-spin`;

export const Button = tw.button<{
  $size: string;
  $variant: string;
  $fullWidth: boolean;
  $disabled: boolean;
}>`
  ${p => (p.$fullWidth ? 'w-full' : 'w-auto')}
  ${p =>
    ({
      small: 'h-8 p-1 text-xs rounded-[4px]',
      large: 'h-12 px-6 text-lg  rounded-lg',
      medium: 'h-10 px-4 text-sm  rounded-lg',
    })[p.$size]}

  leading-6 inline-flex items-center justify-center gap-2
  font-medium border transition-all duration-200

  ${p =>
    ({
      primary:
        'bg-brand-300 text-black-1000 border-brand-300 hover:bg-brand-400 focus:ring-2 focus:ring-brand-300',
      secondary:
        'bg-white text-black-1000 border-grey-100 focus:ring-2 focus:ring-gray-300  hover:bg-grey-50',
      danger:
        'bg-red-10 text-red-200 border-transparent hover:bg-red-600 focus:font-semibold focus:bg-[#FFCCCC] hover:bg-[#FFCCCC] hover:font-semibold',
      outline:
        'bg-white text-black-1000 border border-grey-100 hover:bg-grey-50 focus:ring-2 focus:ring-gray-300',
      link: 'bg-transparent text-blue-200 border-transparent text-[14px] leading-5 font-medium hover:bg-grey-50 outline-none',
    })[p.$variant]}
  ${p => (p.$disabled ? 'opacity-60 cursor-not-allowed' : '')}

  focus:outline-none focus:ring-offset-2
`;
