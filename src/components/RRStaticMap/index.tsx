import { useCallback } from 'react';

import { DeviceStatus } from 'constants/device';
import { MFMap } from 'react-map4d-map';
import { ILatLng } from 'react-map4d-map/dist/models/LatLng';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import { Colors, FontBold } from 'assets/styles';
import { RRLocationPin } from 'components';
import RRCircle from 'components/RRMap/RRCircle';
import RRMaker from 'components/RRMap/RRMaker';

import './styles.css';

export enum PinType {
  Car = 'car',
  Location = 'location',
}

interface RRStaticMapProps {
  position?: ILatLng;
  width?: string;
  height?: string;
  radius?: number;
  showLabel?: boolean;
  rounded?: boolean;
  zoom?: number;
  pinType?: PinType;
  label?: string;
  statisticComponent?: React.ReactNode;
  disabledScroll?: boolean;
}

const RRStaticMap: React.FC<RRStaticMapProps> = ({
  position,
  width,
  height,
  radius,
  rounded = true,
  showLabel = true,
  zoom = 15,
  pinType = PinType.Car,
  statisticComponent,
  label = '50H12345',
  disabledScroll,
}) => {
  const renderPin = useCallback(
    (position: ILatLng) => {
      switch (pinType) {
        case PinType.Car:
          return (
            <RRMaker
              label={label}
              labelAnchor={[0.5, 2]}
              rotation={90}
              position={position}
              status={DeviceStatus.Running}
            />
          );
        case PinType.Location:
          return <RRLocationPin label={label} position={position} />;
        default:
          return (
            <RRMaker
              label={'50H12345'}
              labelAnchor={[0.5, 2]}
              rotation={90}
              position={position}
              status={DeviceStatus.Running}
            />
          );
      }
    },
    [pinType],
  );

  return (
    <>
      <div
        className={`${rounded && `custom-map`} ${
          height ? `h-[${height}]` : 'h-[260px]'
        } ${width ? `w-[${width}]` : 'w-full'} ${showLabel && `py-6`}`}
        style={{
          width,
          height,
          pointerEvents: disabledScroll ? 'none' : 'auto',
        }}
      >
        {showLabel && (
          <div className={`${FontBold} mb-2`}>{t('mapComponent.mapQuery')}</div>
        )}
        <MapContainer>
          <Map
            options={{
              center: position,
              zoom,
            }}
            version={'2.3'}
            accessKey={process.env.ACCESS_KEY ?? ''}
          >
            {!!position && renderPin(position)}
            {radius && position && (
              <RRCircle
                radius={radius}
                fillColor={Colors.grey[300]}
                fillOpacity={0.4}
                strokeColor={Colors.grey[300]}
                strokeWidth={1}
                center={position}
              />
            )}
          </Map>
          <ExtraElement>{statisticComponent}</ExtraElement>
        </MapContainer>
      </div>
    </>
  );
};

export default RRStaticMap;

const Map = tw(MFMap)`rounded-lg`;
const MapContainer = tw.div`relative w-full h-full`;
const ExtraElement = tw.div`
  w-full absolute bottom-0 left-1/2 transform -translate-x-1/2  bg-white p-2 rounded shadow
`;
