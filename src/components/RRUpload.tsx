import React, { useEffect, useMemo, useRef, useState } from 'react';

import { Button, Image, Upload } from 'antd';
import type { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { sortBy, uniqBy } from 'lodash';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';

const { Dragger } = Upload;

const MAX_FILE_SIZE_MB = 25;
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

const beforeUpload = (file: File) => {
  const isLt25MB = file.size <= MAX_FILE_SIZE_BYTES;
  if (!isLt25MB) {
    return Upload.LIST_IGNORE;
  }
  return true;
};

interface RRUploadProps {
  fileList: UploadFile[];
  onChange: (fileList: UploadFile[]) => void;
}

const RRUpload: React.FC<RRUploadProps> = ({ fileList, onChange }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  const numCols = useMemo(
    () => (containerWidth ? Math.floor(containerWidth / 102) : 1) - 1,
    [containerWidth],
  );

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const uniqueFileList = uniqBy([...fileList, ...newFileList], 'uid');
    const sortedFileList = sortBy(uniqueFileList, 'timestamp');
    onChange(sortedFileList);
  };

  const handleRemove = (file: UploadFile) => {
    const newFileList = fileList.filter(f => f.uid !== file.uid);
    onChange(newFileList);
  };

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, []);
  return (
    <Container className='bg-white-1000' ref={containerRef}>
      {fileList.length === 0 ? (
        <Dragger
          beforeUpload={beforeUpload}
          onChange={handleChange}
          style={{ borderStyle: 'solid', backgroundColor: 'white' }}
        >
          <UploadIcon>
            <Icon src={images.Icon.Plus} />
          </UploadIcon>
          <UploadText>{t('upload.upload')}</UploadText>
        </Dragger>
      ) : (
        <>
          <GridContainer
            style={{ gridTemplateColumns: `repeat(${numCols + 1}, 1fr)` }}
          >
            {fileList.map(file => (
              <ImageContainer key={file.uid}>
                <Image
                  key={file.uid}
                  src={URL.createObjectURL(file.originFileObj as Blob)}
                  alt={file.name}
                  wrapperClassName='size-full object-cover rounded-lg'
                  className='size-full rounded-lg'
                  preview={false}
                />
                <Button
                  shape='circle'
                  icon={<Icon src={images.Icon.TrashGrey} />}
                  onClick={() => handleRemove(file)}
                  className='absolute bottom-2 right-2 border-none bg-red-10 shadow-none hover:bg-red-alpha20'
                />
              </ImageContainer>
            ))}
            {fileList.length < 6 && (
              <Upload
                showUploadList={false}
                onChange={handleChange}
                beforeUpload={beforeUpload}
                className='size-[102px] rounded-lg border border-grey-100'
              >
                <UploadButton type='button'>
                  <UploadIcon>
                    <Icon src={images.Icon.Plus} />
                  </UploadIcon>
                  <UploadText>{t('upload.upload')}</UploadText>
                </UploadButton>
              </Upload>
            )}
          </GridContainer>
        </>
      )}
    </Container>
  );
};

export default RRUpload;

const Container = tw.div`w-full `;
const UploadIcon = tw.p`mb-2 flex justify-center`;
const UploadText = tw.p``;
const GridContainer = tw.div`grid gap-2`;
const ImageContainer = tw.div`relative size-[102px] rounded-lg border border-grey-100 p-0.5`;
const UploadButton = tw.button`size-[102px] border-none bg-none`;
