import { useMemo } from 'react';

import { DeviceStatusLabel, StatusColorsMapping } from 'constants/device';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { BodyMdExtend, BodySm, FontSemibold } from 'assets/styles';
import {
  RRFieldDatePicker,
  RRFieldInput,
  RRFieldPassword,
} from 'components/FormField';

import { DeviceFormWrapper, Grid, Label, Status } from '../styles';

const StatusInfoForm = () => {
  const { t } = useTranslation();

  const { control, formState, getValues } = useFormContext();
  const { errors } = formState;

  const speed = getValues('gps.speed');
  const status = getValues('status');
  const statusStyle = useMemo(() => {
    return status ? StatusColorsMapping[status] : {};
  }, [status]);

  return (
    <DeviceFormWrapper>
      <div className='flex flex-col'>
        <Label className={`${BodyMdExtend} ${FontSemibold}`}>
          {t('deviceDetail.status')}
        </Label>
        <Status
          bg={statusStyle?.backgroundColor}
          color={statusStyle.color}
          className={`${BodySm} w-fit`}
        >
          {DeviceStatusLabel[status]}: {speed || 0}km/h
        </Status>
      </div>
      <Grid className='grid-cols-4 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='firmware'
          control={control}
          label={t('deviceDetail.firmware')}
          placeholder={t('deviceDetail.firmware')}
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='coordinates'
          control={control}
          label={t('deviceDetail.coordinates')}
          placeholder={t('deviceDetail.coordinates')}
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='power'
          control={control}
          label={t('deviceDetail.power')}
          placeholder={t('deviceDetail.power')}
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='gsm_signal'
          control={control}
          label={t('deviceDetail.gsmSignalStrength')}
          placeholder={t('deviceDetail.gsmSignalStrength')}
          errors={errors}
          disabled={true}
        />
      </Grid>
      <Grid className='grid-cols-4 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='engine'
          control={control}
          label={t('deviceDetail.engine')}
          placeholder={t('deviceDetail.engine')}
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='ignitionLock'
          control={control}
          label={t('deviceDetail.ignitionLock')}
          placeholder={t('deviceDetail.ignitionLock')}
          errors={errors}
          disabled={true}
        />
        <div className='col-span-2'>
          <RRFieldInput
            id='not_turn_off_the_ignition_time'
            control={control}
            label={t('deviceDetail.forgotToTurnOffLockTime')}
            placeholder={t('deviceDetail.forgotToTurnOffLockTime')}
            errors={errors}
          />
        </div>
      </Grid>
      <Grid className='grid-cols-2 border-b border-grey-100 pb-3'>
        <RRFieldInput
          id='gps.speed'
          control={control}
          label={t('deviceDetail.speed')}
          placeholder={t('deviceDetail.speed')}
          errors={errors}
          disabled={true}
        />
        <RRFieldInput
          id='max_allowable_speed'
          control={control}
          label={t('deviceDetail.speedLimit')}
          placeholder={t('deviceDetail.speedLimit')}
          errors={errors}
        />
      </Grid>
      <Grid className='grid-cols-2'>
        <RRFieldPassword
          id='device_pin'
          control={control}
          label={t('deviceDetail.devicePin')}
          placeholder={t('deviceDetail.devicePin')}
          errors={errors}
        />
        <RRFieldDatePicker
          id='updated_at'
          control={control}
          label={t('deviceDetail.latestUpdate')}
          placeholder={t('deviceDetail.latestUpdate')}
          errors={errors}
          dateFormat='DD/MM/YYYY HH:mm:ss'
          disabled={true}
        />
      </Grid>
    </DeviceFormWrapper>
  );
};

export default StatusInfoForm;
