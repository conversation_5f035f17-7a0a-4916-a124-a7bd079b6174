import { useMemo } from 'react';

import { List } from 'antd';
import tw from 'tailwind-styled-components';

import images from 'assets/images';
import { BodySm } from 'assets/styles';
import { Icon } from 'components';

import './styles.css';
import { CollapseItemData } from './types';

const { Item } = List;

interface Props {
  isActive?: boolean;
  item: CollapseItemData;
  onSelectItem: (item: CollapseItemData) => void;
}

const SearchCollapseItem = (props: Props) => {
  const { isActive, item, onSelectItem } = props || {};

  const iconUrl = useMemo(() => {
    if (isActive) {
      return images.Icon.CheckMark;
    }
    if (item?.is_distributor) {
      return images.Icon.Level1Star;
    }
    if (item?.is_end_user) {
      return images.Icon.Level2Star;
    }
    return '';
  }, [item, isActive]);

  const handleSelectedItem = () => {
    onSelectItem(item);
  };

  return (
    <Item
      onClick={handleSelectedItem}
      className='cursor-pointer px-3 hover:bg-grey-50'
    >
      <NameContainer>
        <Icon src={iconUrl} />
        <div className='flex flex-row items-center gap-2'>
          <div>
            <Text className={`${BodySm}  font-medium  text-black-1000`}>
              {item?.name}
            </Text>
          </div>
          <svg
            width='9'
            height='18'
            viewBox='0 0 9 18'
            fill='none'
            xmlns='http://www.w3.org/2000/svg'
          >
            <path
              d='M6.97727 2.03409L3.22727 15.9659H2L5.75 2.03409H6.97727Z'
              fill='#86888F'
            />
          </svg>

          <div>
            <Text className={`${BodySm}  text-grey-600`}>
              {item?.parent_info?.full_name}
            </Text>
          </div>
        </div>
      </NameContainer>
    </Item>
  );
};

export default SearchCollapseItem;

const NameContainer = tw.div`flex items-center gap-3  overflow-hidden`;
const Text = tw.div`text-grey-600`;
