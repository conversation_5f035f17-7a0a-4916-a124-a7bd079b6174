import { useMemo } from 'react';

import { List } from 'antd';
import { DeviceOptions } from 'constants/device';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import images from 'assets/images';
import { BodySm, FontMedium } from 'assets/styles';
import { Icon } from 'components';
import { LinkButton } from 'components/Button';

import './styles.css';
import { CollapseItemData } from './types';

const { Item } = List;

const DeviceCategory = DeviceOptions.reduce(
  (acc, cur) => {
    acc[cur.value] = cur.label;
    return acc;
  },
  {} as Record<string, string>,
);

const getDeviceCategoryName = (category: string) => {
  return DeviceCategory[category] || category;
};

interface Props {
  showAddMore?: boolean;
  isActive?: boolean;
  isSelectedMultiple?: boolean;
  data: CollapseItemData;
  className?: string;
  onSelect: (
    selectedItem: CollapseItemData,
    path: CollapseItemData[],
    level: number,
  ) => void;
  onLoadMore?: (item: CollapseItemData) => void;
}

const LevelItem = (props: Props) => {
  const { showAddMore, isActive, data, className, onSelect, onLoadMore } =
    props;

  const iconUrl = useMemo(() => {
    if (isActive) {
      return images.Icon.CheckMark;
    }
    if (data?.is_distributor) {
      return images.Icon.Level1Star;
    }
    if (data?.is_end_user) {
      return images.Icon.Level2Star;
    }
    return '';
  }, [data?.is_distributor, data?.is_end_user, isActive]);

  const mainComponent = () => {
    if (data.level === 0) {
      return (
        <NameContainer>
          <Name className={`${BodySm} ${FontMedium}`}>
            {data.name}
            <ParentLegend>
              {data.children_count}/{data.device_count}
            </ParentLegend>
          </Name>
        </NameContainer>
      );
    }

    if (data.level === 1) {
      return (
        <NameContainer>
          <Icon src={iconUrl} />
          <div>
            <Name className={`${BodySm} ${FontMedium}`}>
              <Text>{data.name}</Text>
              {data?.is_distributor && (
                <ChildrenLegend>
                  {data.children_count}/{data.device_count}
                </ChildrenLegend>
              )}
            </Name>
            {data?.is_end_user && !!data?.last_device && (
              <div>
                <Text className='text-grey-600'>
                  {getDeviceCategoryName(data.last_device?.device_category)} -{' '}
                  {data.last_device?.device_imei}
                </Text>
              </div>
            )}
          </div>
        </NameContainer>
      );
    }
    if (data.level === 2) {
      return (
        <NameContainer className='ml-10'>
          <Icon src={iconUrl} />
          <div>
            <div>
              <Text>{data.name}</Text>
              <Text>{data.description}</Text>
            </div>
            {data?.is_end_user && !!data?.last_device && (
              <div>
                <Text className='text-grey-600'>
                  {getDeviceCategoryName(data.last_device?.device_category)} -{' '}
                  {data.last_device?.device_imei}
                </Text>
              </div>
            )}
          </div>
        </NameContainer>
      );
    }
  };

  const handleSelectedItem = () => {
    onSelect(data, [data], data.level);
  };

  const handleLoadMore = () => {
    onLoadMore?.(data);
  };

  return (
    <>
      <Item
        onClick={handleSelectedItem}
        className={twMerge(
          'cursor-pointer hover:bg-grey-50',
          className,
          showAddMore ? 'border-b-0' : '',
        )}
      >
        {mainComponent()}
      </Item>
      {showAddMore && (
        <LinkButton
          onClick={handleLoadMore}
          size='small'
          className='my-3 h-6 text-end'
        >
          Tải thêm
        </LinkButton>
      )}
    </>
  );
};
export default LevelItem;

const NameContainer = tw.div`flex items-center gap-3`;
const Text = tw.div`text-black-1000 text-xs font-normal truncate-1-line max-w-[200px]`;
const Name = tw.div`flex items-center gap-2`;
const ChildrenLegend = tw.div`py-1 px-3 bg-grey-50 text-black-1000 rounded-lg children-legend`;
const ParentLegend = tw.div`py-0.5 px-2 text-white-1000 bg-black-1000 rounded-lg`;
