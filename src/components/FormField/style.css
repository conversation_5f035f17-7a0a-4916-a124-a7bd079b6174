/* Common field */
.navio-form-field {
  width: 100%;
  height: 40px !important;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #000;
  transition: none !important;
  outline: none !important;
  box-shadow: none !important;
  border: 1px solid transparent !important;
  background-color: #f0f1f5;
}

/* type password */
.navio-form-field.password-field input::placeholder {
  font-size: 10px !important;
  letter-spacing: 0.08em !important;
}

.navio-form-field::placeholder,
.navio-form-field input::placeholder {
  color: #54565c !important;
}
/* Input Field */
.navio-form-field:has(.ant-input:focus) {
  border: 1px solid #86888f !important;
  background-color: #e1e3eb !important;
}

/* Select Field */
.ant-select:has(.ant-select-selector:focus) {
  border: 1px solid #86888f !important;
  background-color: #e1e3eb !important;
}

.ant-select-selector:focus {
  border: none !important;
}

.ant-select-selector {
  cursor: pointer !important;
  box-shadow: none !important;
}

.ant-select-selector .ant-select-selection-wrap {
  height: 100%;
}

.navio-form-field .ant-select-selector {
  border: 1px solid transparent !important;
}

.ant-select-selector .ant-select-selection-item {
  padding-inline-end: 24px !important;
}

/* hover */
.navio-form-field:hover {
  background-color: #e1e3eb;
  border: 1px solid #e1e3eb;
}
.navio-form-field.ant-select:hover,
.navio-form-field.ant-select:hover .ant-select-selector {
  background-color: #e1e3eb;
  border: 1px solid transparent !important;
  border-color: transparent !important;
}

/* disabled */
.navio-form-field.ant-picker-disabled,
.navio-form-field.ant-input-disabled,
.navio-form-field.ant-select-disabled,
.navio-form-field:disabled,
.navio-form-field input:disabled {
  background-color: #f0f1f5 !important;
  cursor: not-allowed !important;
  border: 1px solid transparent !important;
  color: #babcc2 !important;
}

.navio-form-field:disabled::placeholder,
.navio-form-field input:disabled::placeholder {
  color: #babcc2 !important;
}

.navio-from-field .ant-select-selector {
  width: 100%;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 400;
  color: #000;
  transition: none !important;
  outline: none !important;
  box-shadow: none !important;
  border: 1px solid transparent !important;
  background-color: #f0f1f5;
}

/* Date picker */
.navio-form-field.ant-picker {
  background-color: #f0f1f5;
}

.ant-picker-panel-container {
  border-radius: 16px !important;
}

.ant-picker-date-panel .ant-picker-header {
  padding: 8px 16px 0px 16px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ant-picker-date-panel .ant-picker-header .ant-picker-header-view {
  color: #000 !important;
  font-size: 14px !important;
  font-weight: 700 !important;
}

.ant-picker-date-panel .ant-picker-header .ant-picker-header-view button:hover {
  color: #000 !important;
}

.ant-picker-header-super-prev-btn,
.ant-picker-header-super-next-btn {
  display: none !important;
}

.ant-picker-date-panel .ant-picker-header {
  border-bottom: none !important;
}

.ant-picker-content thead tr th {
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #000 !important;
}

.ant-picker-body {
  padding: 0px 16px !important;
}

.ant-picker-content .ant-picker-cell .ant-picker-cell-inner {
  border-radius: 8px;
  min-width: 32px;
  height: 32px;
  line-height: 32px;
}

.ant-picker-content
  .ant-picker-cell.ant-picker-cell-today
  .ant-picker-cell-inner::before {
  display: none;
}

.ant-picker-content
  .ant-picker-cell.ant-picker-cell-today
  .ant-picker-cell-inner {
  border: 1px solid #f0f00b;
  background: #ffff9f;
}

.ant-picker-content
  .ant-picker-cell.ant-picker-cell-today
  .ant-picker-cell-inner:hover {
  background: #ffff9f !important;
}

.ant-picker-content .ant-picker-cell .ant-picker-cell-inner:hover {
  background-color: #f0f1f5;
}

.ant-picker-content .ant-picker-cell-selected .ant-picker-cell-inner {
  background-color: #f0f00b !important;
  border-radius: 8px;
  color: #000 !important;
}

.ant-picker-footer {
  border-top: none !important;
}

.ant-picker-footer .ant-picker-now .ant-picker-now-btn {
  width: fit-content;
  border: 1px solid #e1e3eb;
  border-radius: 8px;
  padding: 4px 12px;
  height: 32px;
  line-height: 32px;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 16px;
}

.ant-picker-footer .ant-picker-now .ant-picker-now-btn:hover {
  border-color: #86888f;
  color: #54565c;
}

/* Range Date picker */
