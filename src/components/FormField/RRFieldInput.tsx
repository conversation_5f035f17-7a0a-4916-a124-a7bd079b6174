import React from 'react';

import { Input } from 'antd';
import { getErrorMessage } from 'hooks/useYupResolver';
import { Control, Controller } from 'react-hook-form';

import { Icon } from 'components';

import RRFieldLabel from './RRFieldLabel';
import './style.css';
import {
  CONTAINER_STYLES,
  ICON_STYLES,
  LABEL_STYLES,
  getCombinedInputStyles,
} from './styles';

interface FieldInputProps {
  id: string;
  name?: string;
  label: string;
  control: Control<any>;
  placeholder?: string;
  prefixIcon?: string;
  suffixIcon?: string;
  errors?: any;
  className?: string;
  disabled?: boolean;
  defaultValue?: string;
  required?: boolean;
}

const RRFieldInput: React.FC<FieldInputProps> = ({
  id,
  name,
  required,
  label,
  placeholder,
  control,
  prefixIcon,
  suffixIcon,
  errors = {},
  className,
  disabled,
  defaultValue,
}) => {
  const error = getErrorMessage(errors?.[`${id || name}`]);

  return (
    <Controller
      control={control}
      name={name || id}
      rules={{ required }}
      render={({ field: { onChange, onBlur, value } }) => (
        <div className={CONTAINER_STYLES.base}>
          <RRFieldLabel
            id={id}
            required={required}
            label={label}
            disabled={disabled}
          />
          <Input
            defaultValue={defaultValue}
            className={`${getCombinedInputStyles({
              hasError: !!error,
            })} ${className}`}
            type='text'
            placeholder={placeholder}
            disabled={disabled}
            prefix={
              prefixIcon ? (
                <Icon
                  src={prefixIcon}
                  alt='prefix'
                  className={ICON_STYLES.prefix}
                />
              ) : null
            }
            suffix={
              suffixIcon ? (
                <Icon
                  src={suffixIcon}
                  alt='suffix'
                  className={ICON_STYLES.suffix}
                />
              ) : null
            }
            onChange={onChange}
            onBlur={onBlur}
            value={value}
          />
          {error && (
            <div className={CONTAINER_STYLES.error}>
              <span className={LABEL_STYLES.error}>{error}</span>
            </div>
          )}
        </div>
      )}
    />
  );
};

export default RRFieldInput;
