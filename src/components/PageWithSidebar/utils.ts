import { Abilities, PureAbility } from '@casl/ability';
import { DetailedMenuItem, MultipleSidebarMenuItems } from './types';
import { flattenDeep } from 'lodash';

interface GenerateMenuProps {
  menu: MultipleSidebarMenuItems[];
  context: PureAbility<Abilities, unknown>;
  key?: string;
}

interface GenerateKeysProps {
  menu: DetailedMenuItem[];
  key?: string;
  context: PureAbility<Abilities, unknown>;
}

export const generateMenu = ({
  menu,
  context,
}: GenerateMenuProps): MultipleSidebarMenuItems[] => {
  let parent = 1;
  return Object.keys(menu).reduce((items, value) => {
    const multiMenu = menu[Number(value)];
    if (multiMenu.menu) {
      multiMenu['key'] = `${parent}`;
      multiMenu.menu = parseMenu({
        menu: multiMenu.menu,
        key: `${parent++}`,
        context,
      });
      if (multiMenu.menu.length > 0) {
        items.push(multiMenu as never);
      }
    } else if (items.length > 0) {
      items.push(multiMenu as never);
    }

    return items;
  }, []);
};

export const parseMenu = ({
  menu,
  key = '1',
  context,
}: GenerateKeysProps): DetailedMenuItem[] => {
  let subkey = 1;
  return menu.reduce((menus, item) => {
    const isHasPermission =
      !item.permissions ||
      item.permissions.some((permission: string) => {
        return context.can(permission);
      });

    const menuWithKey = { ...item, key: `${key}.${subkey++}` };
    if (isHasPermission) {
      menus.push(menuWithKey as never);
    }
    return menus;
  }, []);
};

export const generateActiveMenus = (
  menu: DetailedMenuItem[],
  currentRoute: string,
) => {
  const activeItems: DetailedMenuItem[] = flattenMenu(menu).filter(
    ({ route, exact }) =>
      route &&
      (exact ? currentRoute === route : currentRoute.indexOf(route) === 0),
  );

  const exactRouteRequiredItems = activeItems.filter(({ exact }) => exact);

  return (
    exactRouteRequiredItems.length ? exactRouteRequiredItems : activeItems
  ).map(({ key }) => key);
};

export const flattenMenu = (menu: DetailedMenuItem[]): DetailedMenuItem[] => {
  return menu?.reduce((flat, item) => {
    if (item.route) {
      flat.push(item as never);
    }
    return flat;
  }, []);
};

export const getMenus = (menu: DetailedMenuItem[]): DetailedMenuItem[] => {
  const extractMenus = items => {
    return items.flatMap(item => (item.menu ? extractMenus(item.menu) : item));
  };

  return flattenDeep(extractMenus(menu));
};
