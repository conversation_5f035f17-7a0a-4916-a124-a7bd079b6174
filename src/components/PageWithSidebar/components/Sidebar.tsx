import React from 'react';

import { flushSync } from 'react-dom';

import { useNavigate } from 'react-router-dom';

import { useOpenKeys, useSelectedKeys } from '../hooks';
import {
  MenuSliderWrapper,
  SidebarContainer,
  SidebarMenu,
  SidebarMenuContainer,
} from '../styles';
import { MultipleSidebarMenuItems } from '../types';
import usePageWithSidebarContext from '../usePageWithSidebarContext';
import ProfileCard from './ProfileCard';
import SidebarHeader from './SidebarHeader';
import menuItemMapper from './menuItemMapper';

interface Props {
  menu: MultipleSidebarMenuItems[];
  currentMenu: number;
  updateCurrentMenu: (index: number) => void;
}

const Sidebar: React.FC<Props> = ({
  menu,
  currentMenu = 0,
  updateCurrentMenu,
}) => {
  const navigate = useNavigate();
  const { collapsed, onCollapseClick, onUpdateStatusCollapsed } =
    usePageWithSidebarContext();

  const { openKeys, setOpenKeys } = useOpenKeys();
  const { selectedKeys, updateSelectedKeys } = useSelectedKeys(
    menu,
    currentMenu,
    updateCurrentMenu,
    openKeys,
    setOpenKeys,
  );

  const onClickItem = (route: string) => {
    // Force immediate state update using flushSync to eliminate batching delay
    flushSync(() => {
      if (route === '/map') {
        onUpdateStatusCollapsed(true);
      } else {
        onUpdateStatusCollapsed(false);
      }
    });

    // Navigate immediately - animation starts instantly
    navigate(route);
  };

  const menuItemHandlers = {
    openKeys: openKeys || [],
    setOpenKeys,
    onClickItem,
    selectedKeys: selectedKeys || [],
    updateSelectedKeys,
  };

  return (
    <SidebarContainer collapsed={collapsed}>
      <SidebarHeader collapsed={collapsed} onCollapseClick={onCollapseClick} />
      <MenuSliderWrapper>
        <SidebarMenuContainer>
          <SidebarMenu
            mode='inline'
            selectedKeys={selectedKeys}
            openKeys={openKeys}
            className='sidebar-menu'
          >
            {menu.map(menuItemMapper(menuItemHandlers, collapsed))}
          </SidebarMenu>
        </SidebarMenuContainer>
      </MenuSliderWrapper>

      <ProfileCard collapsed={collapsed} />
    </SidebarContainer>
  );
};

export default Sidebar;
