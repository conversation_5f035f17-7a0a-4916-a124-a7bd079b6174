import { useCallback, useContext, useEffect, useState } from 'react';

import { Abilities, PureAbility } from '@casl/ability';
import { Route, Routes, useNavigate } from 'react-router-dom';

import { AbilityContext } from 'features/Core/App/utils/permissions';
import Profile from 'features/Profile';

import Sidebar from './components/Sidebar';
import Context from './context';
import { MainContainer, PageContainer } from './styles';
import { MultipleSidebarMenuItems } from './types';
import { generateMenu, getMenus } from './utils';

interface Props {
  menu: MultipleSidebarMenuItems[];
}

const LOCAL_STORAGE_SB_KEY = 'sidebarCollapsed';

const PageWithSidebar = ({ menu }: Props) => {
  const navigate = useNavigate();
  const abilityContext: PureAbility<Abilities, unknown> =
    useContext(AbilityContext);
  const [menuWithKeys, updateMenuWithKeys] = useState<
    MultipleSidebarMenuItems[]
  >([]);
  const [currentMenu, updateCurrentMenu] = useState<number>(0);
  const [collapsed, updateCollapsed] = useState<boolean>(
    localStorage.getItem(LOCAL_STORAGE_SB_KEY) === 'true',
  );

  const allMenus = getMenus(menu);
  const isCorrectRouting =
    allMenus.some(item => item?.route === window.location.pathname) ||
    window.location.pathname === '/profile';

  const isCorrectSubRouting = allMenus.some(
    item =>
      item?.subItems?.some(
        subItem => subItem.route === window.location.pathname,
      ),
  );

  const onCollapseClick = useCallback(() => {
    if (!collapsed) {
      updateCollapsed(true);
      localStorage.setItem(LOCAL_STORAGE_SB_KEY, 'true');
    } else {
      updateCollapsed(false);
      localStorage.setItem(LOCAL_STORAGE_SB_KEY, 'false');
    }
  }, [collapsed]);

  const onUpdateStatusCollapsed = useCallback((value: boolean) => {
    updateCollapsed(value);
    localStorage.setItem(LOCAL_STORAGE_SB_KEY, value.toString());
  }, []);

  useEffect(() => {
    updateMenuWithKeys(
      generateMenu({
        menu,
        context: abilityContext,
      }),
    );
  }, [menu, abilityContext]);

  useEffect(() => {
    if (!isCorrectRouting && !isCorrectSubRouting) {
      navigate(allMenus[0]?.route ?? '/');
    }
  }, [isCorrectRouting]);

  return (
    <Context.Provider
      value={{
        collapsed,
        onCollapseClick,
        onUpdateStatusCollapsed,
      }}
    >
      <PageContainer>
        {menuWithKeys && (
          <Sidebar
            menu={menuWithKeys}
            currentMenu={currentMenu}
            updateCurrentMenu={updateCurrentMenu}
          />
        )}
        <MainContainer
          className={`duration-250 transition-all ease-[cubic-bezier(0.4,0,0.2,1)] will-change-[width] ${
            collapsed ? 'w-[calc(100%-80px)]' : 'w-[calc(100%-240px)]'
          }`}
        >
          <Routes>
            {Object.keys(menu).reduce((items, value) => {
              let key = 0;
              (menu[Number(value)].menu ?? []).forEach(item => {
                if (item.component) {
                  items.push(
                    (
                      <Route
                        key={key++}
                        path={item.route}
                        element={<item.component />}
                      />
                    ) as never,
                  );
                } else if (item?.subItems?.length) {
                  item.subItems.forEach(subItem => {
                    items.push(
                      (
                        <Route
                          key={key++}
                          path={subItem.route}
                          element={
                            subItem.component ? <subItem.component /> : null
                          }
                        />
                      ) as never,
                    );
                  });
                }
              });
              return items;
            }, [])}
            <Route key={'profile'} path='/profile' element={<Profile />} />
          </Routes>
        </MainContainer>
      </PageContainer>
    </Context.Provider>
  );
};

export default PageWithSidebar;
