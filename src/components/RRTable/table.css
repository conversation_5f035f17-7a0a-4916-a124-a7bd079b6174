/* Sticky header styling */
.custom-table-rrtable .ant-table-thead > tr > th {
  height: 39px !important;
  padding: 0 16px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  color: #54565c;
  position: sticky !important;
  top: 0 !important;
  background: #f0f1f5 !important;
  z-index: 1 !important;
  border-bottom: 1px solid #e1e3eb !important;
}

.custom-table-rrtable .ant-table-thead .ant-table-cell {
  border-color: #e1e3eb !important;
}

/* Table body styling */
.custom-table-rrtable .ant-table-tbody > tr > td {
  padding: 8px 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  min-width: 80px;
  border-color: #e1e3eb !important;
}

:where(.css-dev-only-do-not-override-6xu9vw).ant-table-wrapper
  .ant-table-tbody
  .ant-table-row
  > .ant-table-cell-row-hover {
  background-color: #f0f1f5 !important;
}

.custom-table-rrtable .ant-table-tbody > tr:hover td {
  background-color: #f0f1f5 !important;
}

.custom-table-rrtable .ant-table-tbody > tr:hover {
  background-color: #f0f1f5 !important;
}

/* Scrollable body */
.custom-table-rrtable .ant-table-body {
  overflow-y: auto !important;
  overflow-x: auto !important;
}

.device-log-table.custom-table-rrtable .ant-table-tbody > tr > td {
  max-width: fit-content;
}
