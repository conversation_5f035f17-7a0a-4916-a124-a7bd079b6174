import React from 'react';

import { Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import tw from 'tailwind-styled-components';

import Pagination from 'components/Pagination';

import './table.css';

interface RRTableProps {
  className?: string;
  columns: ColumnsType<Record<string, unknown>>;
  data: Record<string, unknown>[];
  hidePagination?: boolean;
  height?: number | string; // Height for scrollable body
  loading?: boolean; // Loading state
  tableLayout?: 'fixed' | 'auto';

  currentPage: number;
  pageSize: number;
  total: number;
  onPageChange?: (page: number, pageSize: number) => void;
  onPageSizeChange?: (size: number) => void;
}

const RRTable: React.FC<RRTableProps> = ({
  className,
  tableLayout = 'fixed',
  columns,
  data,
  total,
  currentPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
  height = 50 * 5,
  hidePagination,
  loading = false,
}) => {
  return (
    <Body>
      <Table
        bordered
        columns={columns}
        dataSource={data}
        pagination={false}
        className={`custom-table-rrtable ${className || ''}`}
        scroll={{ y: height }}
        tableLayout={tableLayout}
        loading={loading}
      />
      {!hidePagination && (
        <Pagination
          className='mt-4'
          page={currentPage}
          pageSize={pageSize}
          total={total}
          onPageChange={onPageChange}
          onChangePerPage={onPageSizeChange}
        />
      )}
    </Body>
  );
};

export default RRTable;

const Body = tw.div`w-full flex flex-1 flex-col h-full`;
