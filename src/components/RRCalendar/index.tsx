import React, { useState } from 'react';

import { Calendar, Popover } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import tw from 'tailwind-styled-components';

import i18n from 'i18n';

import images from 'assets/images';

import RRSvgIcon from '../RRSvgIcon';
import './RRCalendar.css';

type Props = {
  startDate?: Dayjs;
  endDate?: Dayjs;
  className?: string;
  minDateTime?: Date;
  maxDateTime?: Date;
  onSelect?: (type?: 'start' | 'end', date?: Dayjs) => void;
  calendarWidth?: number;
  placeholder?: string;
  classNameInput?: string;
};
export enum StandardFormats {
  CALENDAR_DATE = 'YYYY-MM-DD',
  USER_DATE = 'DD/MM/YYYY',
}

const DEFAULT_WIDTH = 360;

const RRCalendar: React.FC<Props> = ({
  onSelect,
  minDateTime,
  maxDateTime,
  startDate,
  endDate,
  calendarWidth = DEFAULT_WIDTH,
  placeholder,
  className,
  classNameInput,
}) => {
  // const [startDate, setStartDate] = useState<Dayjs>(dayjs());
  // const [endDate, setEndDate] = useState<Dayjs | undefined>(dayjs());
  const [dateSelectionState, setDateSelectionState] = useState<'start' | 'end'>(
    'start',
  );
  const [currentMonth, setCurrentMonth] = useState(dayjs());
  const [isCalendarVisible, setCalendarVisible] = useState(false);

  const handleSelect = (date: Dayjs) => {
    const clampedDate = clampDate(date, minDateTime, maxDateTime);
    if (dateSelectionState === 'start') {
      onSelect?.(dateSelectionState, clampedDate);
      onSelect?.('end', undefined);
      setDateSelectionState('end');
      return;
    }
    const newEndDate = clampedDate.isBefore(startDate, 'day')
      ? startDate
      : clampedDate;
    onSelect?.('end', newEndDate);

    if (clampedDate.isBefore(startDate, 'day')) {
      onSelect?.('start', clampedDate);
    }
    setCalendarVisible(false);
  };
  const clampDate = (date: Dayjs, minDate?: Date, maxDate?: Date) => {
    if (minDate && date.isBefore(minDate)) {
      return dayjs(minDate);
    }
    if (maxDate && date.isAfter(maxDate)) {
      return dayjs(maxDate);
    }
    return date;
  };

  const isDateInRange = (date: Dayjs) => {
    const isStart = date.isSame(startDate, 'day');
    const isEnd = endDate && date.isSame(endDate, 'day');
    return {
      isHighLight:
        (date.isAfter(startDate, 'day') &&
          endDate &&
          date.isBefore(endDate, 'day')) ||
        isStart ||
        isEnd,
      isStart: isStart,
      isEnd: isEnd,
    };
  };

  const handlePrevMonth = () => {
    setCurrentMonth(currentMonth.subtract(1, 'month'));
  };

  const handleNextMonth = () => {
    setCurrentMonth(currentMonth.add(1, 'month'));
  };
  const handleSelectionDate = (type: typeof dateSelectionState) => {
    setDateSelectionState(type);
    setCalendarVisible(!isCalendarVisible);
  };

  const disabledDate = (current: Dayjs) => {
    const minDay = minDateTime ? dayjs(minDateTime) : null;
    const maxDay = maxDateTime ? dayjs(maxDateTime) : null;

    if (minDay && current.isBefore(minDay, 'day')) return true;
    if (maxDay && current.isAfter(maxDay, 'day')) return true;
    return false;
  };

  const handleQuickSelect = (start: Dayjs, end: Dayjs) => {
    // TODO: will refactor later
    const clampedStart = clampDate(start, minDateTime, maxDateTime);
    const clampedEnd = clampDate(end, minDateTime, maxDateTime);

    onSelect?.('start', clampedStart);
    onSelect?.('end', clampedEnd);
    setCalendarVisible(false);
  };

  const dateFullCellRender = (value: Dayjs) => {
    const { isHighLight, isEnd, isStart } = isDateInRange(value);

    const cellChild = isStart || isEnd ? 'rounded-lg bg-brand-300 ' : '';
    let cellContainerStyle = isStart ? 'rounded-l-lg ' : '';
    cellContainerStyle += isEnd ? 'rounded-r-lg ' : '';

    return (
      <div className={cellContainerStyle}>
        <div
          className={`mx-0.5 flex h-10 items-center justify-center rounded-lg hover:bg-grey-100 ${
            isHighLight ? 'text-black-1000 ' : 'text-orange-500 '
          } ${cellChild}`}
        >
          {value.date()}
        </div>
      </div>
    );
  };

  const calendarContent = (
    <CalendarPickContainer width={calendarWidth}>
      <Calendar
        className='custom-calendar'
        fullscreen={false}
        onSelect={handleSelect}
        value={currentMonth}
        fullCellRender={dateFullCellRender}
        disabledDate={disabledDate}
        headerRender={() => (
          <div className='mb-4 flex items-center justify-between'>
            <ArrowButton onClick={handlePrevMonth}>
              <RRSvgIcon src={images.Icon.ChevronLeft} />
            </ArrowButton>
            <div className='text-center font-bold'>
              {i18n.t('calendar.currentMonthLabel', {
                month: currentMonth.format('M, YYYY'),
              })}
            </div>

            <ArrowButton onClick={handleNextMonth}>
              <RRSvgIcon src={images.Icon.ChevronRight} />
            </ArrowButton>
          </div>
        )}
      />
      <Label>{i18n.t('calendar.quickOption')}</Label>
      <QuickSelectSection
        className={`${isCalendarVisible ? 'visible' : 'invisible'}`}
      >
        <QuickButton
          onClick={() => {
            handleQuickSelect(
              dayjs().startOf('isoWeek'),
              dayjs().endOf('isoWeek'),
            );
          }}
        >
          {i18n.t('calendar.thisWeek')}
        </QuickButton>
        <QuickButton
          onClick={() => {
            handleQuickSelect(dayjs().subtract(7, 'day'), dayjs().endOf('day'));
          }}
        >
          {i18n.t('calendar.sevenday')}
        </QuickButton>
        <QuickButton
          onClick={() => {
            handleQuickSelect(
              dayjs().subtract(30, 'day'),
              dayjs().endOf('day'),
            );
          }}
        >
          {i18n.t('calendar.past30day')}
        </QuickButton>
      </QuickSelectSection>
    </CalendarPickContainer>
  );

  return (
    <Popover
      content={calendarContent}
      trigger='click'
      open={isCalendarVisible}
      onOpenChange={visible => setCalendarVisible(visible)}
      overlayClassName='rr-calendar-popover'
    >
      <Container className={className}>
        <CalendarInput
          className={`${classNameInput} hover:bg-grey-100`}
          onClick={() => handleSelectionDate('start')}
        >
          {startDate && endDate && (
            <>
              <DateText>
                {startDate?.format(StandardFormats.USER_DATE)}
              </DateText>
              <Arrow>
                <RRSvgIcon src={images.Icon.ArrowRight} />
              </Arrow>
              <DateText>
                {endDate?.format(StandardFormats.USER_DATE) ??
                  StandardFormats.USER_DATE}
              </DateText>
            </>
          )}
          {placeholder && (!startDate || !endDate) && (
            <DateText>{placeholder}</DateText>
          )}
          <Arrow>
            <RRSvgIcon src={images.Icon.CalendarSchedule} />
          </Arrow>
        </CalendarInput>
      </Container>
    </Popover>
  );
};
const Container = tw.div`w-full flex flex-col items-center`;
const CalendarInput = tw.button`
  w-full flex items-center justify-between
  bg-grey-50 p-3 rounded-lg
  focus:outline-none focus:ring-0 h-10
  focus:shadow-none
`;
const DateText = tw.button`
  text-black font-regular text-sm overflow-hidden whitespace-nowrap text-ellipsis
`;
const Arrow = tw.span`
  mx-2 text-gray-500
`;
const QuickSelectSection = tw.div`flex space-x-2 mt-2`;
const QuickButton = tw.button`
  flex-auto
  bg-gray-100 px-3 py-1 rounded-md border-grey-100 border-[1.5px]
  focus:ring-2 focus:ring-indigo-500
  hover:border-grey-400 hover:text-grey-600
`;
const CalendarPickContainer = tw.div<{
  width: number;
}>`bg-white
 ${props => props.width && `w-[${props.width}px]`}`;

const Label = tw.span`w-full text-sm leading-[20px] font-medium font-web-body-md-base-medium text-grey-400`;
const ArrowButton = tw.button`rounded-button-radiuscorner flex flex-row items-center justify-center`;

export default RRCalendar;
