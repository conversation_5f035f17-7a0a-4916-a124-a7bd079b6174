import React from 'react';

import dayjs, { Dayjs } from 'dayjs';
import tw from 'tailwind-styled-components';

import { HistoryData, OverviewData } from 'features/Map/types';
import { t } from 'i18next';
import type { DeviceType } from 'types/device';

import images from 'assets/images';
import { H4 } from 'assets/styles';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import DeviceJourneyDetailInfo from './DeviceJourneyDetailInfo';
import { convertMeterToKm, formatDuration } from './utils';

interface DeviceJourneyInfoProps {
  className?: string;
  onPreviewClick?: (date?: Dayjs, data?: any) => void;
  historyData: HistoryData;
  overviewData?: OverviewData;
  device?: DeviceType;
}

const DeviceJourneyInfo: React.FC<DeviceJourneyInfoProps> = ({
  className,
  onPreviewClick,
  historyData,
  overviewData,
  device,
}) => {
  const { data = [] } = historyData || {};

  return (
    <DeviceJourneyInfoWrapper id='history' className={className}>
      <div className='flex flex-col gap-3 transition-all duration-500'>
        <InfoSection>
          <i className={`${H4} font-bold leading-[40px]`}>
            {convertMeterToKm(overviewData?.total_distance)}
          </i>
          <div className='text-sm font-medium leading-[20px] text-grey-600'>
            {t('map.movingRoad')}
          </div>
        </InfoSection>
        <div className='flex flex-row items-start justify-start gap-10 self-stretch text-xs text-grey-600'>
          <div className='flex flex-row items-start justify-start gap-2 overflow-hidden'>
            <img className='relative size-4' src={images.Icon.ParkingSign} />
            <div className='flex flex-col items-start justify-start gap-0.5'>
              <MainLabel>{t('map.movingTiming')}</MainLabel>
              <SubLabel>{formatDuration(overviewData?.move_time)}</SubLabel>
            </div>
          </div>
          <div className='flex flex-row items-start justify-start gap-2 overflow-hidden'>
            <img
              className='relative size-4'
              src={images.Icon.WarningElectric}
            />
            <div className='flex flex-col items-start justify-start gap-0.5'>
              <MainLabel>{t('map.stopTiming')}</MainLabel>
              <SubLabel>{formatDuration(overviewData?.stop_time)}</SubLabel>
            </div>
          </div>
        </div>
      </div>
      <div className='h-[calc(100vh-296px)]'>
        <RRScrollView className='w-[calc(100%+11px)]'>
          <div className='w-[calc(100%-11px)]'>
            {data.map(item => {
              const currentDate = dayjs(item?.time_from);
              return (
                <Container key={item?.time_from?.toString()}>
                  <DeviceJourneyDetailInfo
                    onPreviewClick={() => {
                      onPreviewClick?.(currentDate, item);
                    }}
                    selectedDate={currentDate}
                    data={item}
                    timezone={device?.timezone}
                  />
                </Container>
              );
            })}
          </div>
        </RRScrollView>
      </div>
    </DeviceJourneyInfoWrapper>
  );
};

export default DeviceJourneyInfo;

const DeviceJourneyInfoWrapper = tw.div`flex flex-col`;
const Container = tw.div`flex flex-col items-center border-b border-grey-100 border-solid pb-3`;
const InfoSection = tw.div`flex flex-col`;
const SubLabel = tw.div`text-sm font-semibold leading-[20px] text-black-1000`;
const MainLabel = tw.div`font-medium leading-[16px]`;
