import * as React from 'react';

import { Tooltip } from 'antd';
import { DeviceStatusLabel, StatusColorsMapping } from 'constants/device';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import useDeviceAddress from 'features/DeviceBusiness/hooks/useDeviceAddress';
import { useMapGeocodeReverse } from 'features/Map/hooks/useMapData';
import { t } from 'i18next';

import { ChevronDownIcon } from 'assets/icons';
import images from 'assets/images';

import { bottomItemActionListing } from './common';

const ListCustomItem = ({
  label,
  value,
  className = '',
  classNameValue = '',
}) => {
  return (
    <div className={twMerge('relative', className)}>
      <span>{label}</span>
      <span className={twMerge('ml-1 text-black-1000', classNameValue)}>
        {value}
      </span>
    </div>
  );
};

const getTimeUpdated = (date, timezone) => {
  if (!date) return '';
  return dayjs(date).tz(timezone).format('DD/MM/YYYY HH:mm:ss');
};

const DeviceMapActions: React.FC<any> = ({
  isRealTime,
  className,
  device,
  onItemActionClick,
  selectedItem,
  currentPoint,
}) => {
  const navigate = useNavigate();

  const deviceAddress = useDeviceAddress(device);
  const realTimeAddress = useMapGeocodeReverse({
    lat: currentPoint?.latitude,
    lng: currentPoint?.longitude,
  }) as { data: any | undefined };

  const [expanded, setExpanded] = React.useState(false);

  const infoDevice = React.useMemo(() => {
    if (isRealTime) {
      return {
        speed: currentPoint?.speed || 0,
        coordinate: `${currentPoint?.latitude},${currentPoint?.longitude}`,
        status: currentPoint?.status,
        address: realTimeAddress,
        updatedAt: currentPoint?.timestamp,
        position: {
          lat: currentPoint?.latitude,
          lng: currentPoint?.longitude,
        },
        timezone: device?.timezone,
        ...(StatusColorsMapping[currentPoint?.status] || {}),
      };
    }
    return {
      speed: device?.gps?.speed || 0,
      coordinate: `${device?.gps?.latitude},${device?.gps?.longitude}`,
      status: device?.status,
      address: deviceAddress,
      updatedAt: device?.gps?.timestamp,
      position: {
        lat: device?.gps?.latitude,
        lng: device?.gps?.longitude,
      },
      timezone: device?.timezone,
      ...(StatusColorsMapping[device?.status] || {}),
    };
  }, [isRealTime, currentPoint]);

  if (!device) {
    return;
  }

  const handleRedirectToDriver = () => {
    // profile?active=driver
    navigate(`/profile?active=driver`);
  };

  const handleOpenGoogleMap = () => {
    window.open(
      `https://www.google.com/maps/search/?api=1&query=${infoDevice.position.lat},${infoDevice.position.lng}`,
      '_blank',
    );
  };

  return (
    <Container className={className}>
      <ContentContainer>
        <ChildContainer className='h-[200px]'>
          {bottomItemActionListing.map(item => {
            return (
              <Tooltip
                className='menu-collapse-tooltip'
                title={item.title}
                placement='left'
                arrow={{
                  pointAtCenter: true,
                  arrowPointAtCenter: true,
                }}
                color='white'
                overlayInnerStyle={{
                  padding: '12px 16px',
                  borderRadius: 'var(--Tooltip-radiusCorner, 8px)',
                  background: 'var(--Tooltip-background, #FFF)',
                  boxShadow: '0px 1px 8px 0px rgba(0, 0, 0, 0.20)',
                  color: '#000',
                  fontSize: '14px',
                  fontWeight: '500',
                }}
                key={item.source}
              >
                <ActionContainer
                  role='button'
                  onClick={() => onItemActionClick?.(item.type)}
                >
                  <ActionIcon
                    className={`${
                      selectedItem === item.type ? 'border-black-1000' : ''
                    }`}
                  >
                    <img src={item.source} />
                  </ActionIcon>
                </ActionContainer>
              </Tooltip>
            );
          })}
        </ChildContainer>
        <ChildContainer className='w-[248px] shrink-0 p-4'>
          <div className='mb-3 flex flex-col items-start justify-start gap-2 self-stretch'>
            <div className='flex flex-row items-center justify-between self-stretch text-grey-400'>
              <div className='flex flex-row items-center justify-start'>
                <div className='relative font-medium leading-[16px]'>
                  {`${t('map.deviceInfo')}`}
                </div>
              </div>
              {/* rotate down and up */}
              <div
                className={`cursor-pointer text-black-1000 transition-transform duration-200 ease-in-out ${
                  expanded ? 'rotate-180' : ''
                }`}
                role='button'
                onClick={() => setExpanded(prev => !prev)}
              >
                <ChevronDownIcon />
              </div>
            </div>
            <b className='relative self-stretch leading-[16px] text-black-1000'>
              {device?.device_plate_number} -{device?.device_name}
            </b>
            <Status
              bg={infoDevice?.backgroundColor}
              color={infoDevice.color}
              className={`w-fit text-xs`}
            >
              {DeviceStatusLabel[infoDevice.status]}: {infoDevice?.speed || 0}
              km/h
            </Status>
            <ListCustomItem
              label={`${t('map.licenseNo')}:`}
              value={device?.device_plate_number}
            />
            <ListCustomItem
              label={`${t('map.address')}:`}
              value={infoDevice.address || ''}
            />
            <ListCustomItem
              label={`${t('map.lastUpdated')}:`}
              value={getTimeUpdated(infoDevice.updatedAt, infoDevice.timezone)}
            />
            <ListCustomItem
              label={`${t('map.location')}:`}
              className='flex w-full items-center'
              classNameValue='text-blue-200 [text-decoration:underline]'
              value={
                <div
                  className='cursor-pointer'
                  role='button'
                  onClick={handleOpenGoogleMap}
                >
                  {infoDevice.coordinate}
                </div>
              }
            />
          </div>
          {expanded && (
            <>
              <div className='mb-3 flex flex-col items-start justify-start gap-2 self-stretch'>
                <div className='flex flex-row items-center justify-start text-grey-400'>
                  <div className='relative font-medium leading-[16px]'>
                    {t('map.otherData')}
                  </div>
                </div>
                <ListCustomItem
                  label='3G/4G:'
                  value={`${device?.gsm_signal}%`}
                />
                <ListCustomItem label='GPS:' value='-' />
                <ListCustomItem
                  label={`${t('map.accumulator')}:`}
                  value={`${device?.power}V`}
                />
                <Row>
                  <div className='flex flex-row items-start justify-start gap-0.5'>
                    <div className='relative leading-[16px]'>{`${t(
                      'map.ignitionSwitch',
                    )}:`}</div>
                  </div>
                  <div className='flex flex-row items-center justify-center gap-1 text-green-200'>
                    <div className='relative font-semibold leading-[16px]'>
                      {String(device?.ignitionLock) === '1' ? 'Bật' : 'Tắt'}
                    </div>
                    <div className='relative font-semibold leading-[16px] text-black-1000'>
                      (-)
                    </div>
                  </div>
                </Row>
                <Row>
                  <div className='flex flex-row items-start justify-start gap-0.5'>
                    <div className='relative leading-[16px]'>{`${t(
                      'map.engine',
                    )}:`}</div>
                  </div>
                  <div className='flex flex-row items-center justify-center gap-1 text-green-200'>
                    <div className='relative font-semibold leading-[16px]'>
                      {String(device?.engine) === '1' ? 'Bật' : 'Tắt'}
                    </div>
                    <div className='relative font-semibold leading-[16px] text-black-1000'>
                      (-)
                    </div>
                  </div>
                </Row>
              </div>
              <div className='flex flex-col items-start justify-start gap-1 self-stretch border-t border-grey-100 pt-3'>
                <div className='flex flex-row items-center justify-between self-stretch text-grey-400'>
                  <div className='flex flex-row items-center justify-start'>
                    <div className='relative font-medium leading-[16px]'>
                      {`${t('map.driverInfo')}`}
                    </div>
                  </div>
                  <div
                    role='button'
                    className='flex cursor-pointer items-center justify-center gap-1'
                    onClick={handleRedirectToDriver}
                  >
                    <img
                      className='relative size-4'
                      src={images.Icon.PlusCircle}
                    />
                  </div>
                </div>
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Họ tên:'
                  value={
                    device?.driver?.driver_license_and_name?.split(',')?.[0] ||
                    ''
                  }
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label='Giấy phép:'
                  value={
                    device?.driver?.driver_license_and_name?.split(',')?.[1] ||
                    ''
                  }
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingOnTheTime')}:`}
                  value={`${
                    device?.operation?.time_of_continuous_driving || 0
                  } phút`}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingDuringday')}:`}
                  value={`${device?.operation?.driving_time_per_day || 0} phút`}
                />
                <ListCustomItem
                  className='flex w-full items-center justify-between'
                  label={`${t('map.drivingOver4h')}:`}
                  value={`${device?.operation?.over_4h_count || 0} phút`}
                />
              </div>
            </>
          )}
        </ChildContainer>
      </ContentContainer>
    </Container>
  );
};

export default DeviceMapActions;

const Container = tw.div`flex flex-col items-end justify-start bg-transparent text-left text-xs text-grey-600`;
const ContentContainer = tw.div`flex flex-row items-start justify-center gap-2`;
const ChildContainer = tw.div`box-border flex flex-col items-start justify-between rounded-xl bg-white-1000 p-2 shadow-[0px_1px_8px_rgba(0,_0,_0,_0.12)]`;
const ActionContainer = tw.div`flex w-8 flex-row items-start justify-start self-stretch cursor-pointer`;
const ActionIcon = tw.div`p-space-inset-3 box-border flex size-8 flex-row items-center justify-center rounded-lg border-[1.5px] border-solid border-grey-100`;
const Row = tw.div`flex flex-row items-start justify-between self-stretch relative leading-[16px]`;

export const Status = tw.div<{ bg: string; color: string }>`
  rounded-md px-1.5 py-1 inline-block
  ${props => props.bg && `${props.bg}`}
  ${props => props.color && `${props.color}`}
`;
