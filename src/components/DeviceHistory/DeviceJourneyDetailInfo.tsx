import * as React from 'react';
import { useCallback, useMemo } from 'react';

import { Dayjs } from 'dayjs';
import { capitalize, get } from 'lodash';
import tw from 'tailwind-styled-components';

import { useMapGeocodeReverse } from 'features/Map/hooks/useMapData';
import { DataItem } from 'features/Map/types';

import images from 'assets/images';
import { LinkButton } from 'components/Button';

import { convertMeterToKm, formatTimeRange } from './utils';

export enum HistoryStatus {
  Lost = 'lost',
  Stop = 'stop',
  Run = 'run',
  Begin = 'begin_route',
}

const HistoryStopRow = ({ data, timezone }) => {
  const addressData = useMapGeocodeReverse({
    lat: String(data?.start?.latitude) || '',
    lng: String(data?.start?.longitude) || '',
  }) as { data: any | undefined };
  return (
    <>
      <RightContainer>
        <SpaceTop />
        <ContainerStepLabel>
          <StepLabel>{data?.stopCount}</StepLabel>
        </ContainerStepLabel>
        <BreakLine />
      </RightContainer>
      <CenterContainer>
        <span className='text-left font-semibold text-grey-400'>
          {String(addressData) || ''}
        </span>

        <div className='gap-space-inline-8 text-text-primary flex flex-row items-center justify-start'>
          <ClockIcon src={images.Icon.ClockWatch} />
          <div className='leading-[16px]'>
            {formatTimeRange({
              startTimestamp: data?.start?.timestamp,
              endTimestamp: data?.end?.timestamp,
              timezone,
            })}
          </div>
        </div>
      </CenterContainer>
    </>
  );
};

type Props = {
  selectedDate?: Dayjs;
  onPreviewClick?: () => void;
  hideSectionHeader?: boolean;
  data: DataItem;
  timezone?: string;
};
const DeviceJourneyDetailInfo: React.FC<Props> = ({
  selectedDate,
  onPreviewClick,
  hideSectionHeader,
  data,
  timezone,
}) => {
  const stepIndex = React.useRef(0);
  const routes = useMemo(() => get(data, 'routes', []), [data]);

  const Header = useMemo(() => {
    const dayOfWeek = selectedDate
      ?.format('dddd')
      ?.split(' ')
      .map(word => capitalize(word))
      .join(' ');
    const fullDate = selectedDate?.format('DD/MM/YYYY');
    return (
      <div className='box-border flex w-full flex-row items-center justify-between pb-2 pr-0 pt-3 text-sm text-grey-600'>
        <b className='relative truncate leading-[20px]'>
          {`${dayOfWeek}, ${fullDate}`}
        </b>
        <LinkButton
          onClick={onPreviewClick}
          size='small'
          className='my-3 h-6 text-end'
        >
          Xem lại
        </LinkButton>
      </div>
    );
  }, [selectedDate]);

  const renderRow = useCallback(data => {
    switch (data?.type) {
      case HistoryStatus.Stop: {
        stepIndex.current += 1;
        return <HistoryStopRow data={data} timezone={timezone} />;
      }
      case HistoryStatus.Run:
        return (
          <>
            <RightContainer>
              <CompletedStatus>
                <div className='flex-1 self-stretch rounded-[50%] bg-green-200' />
              </CompletedStatus>

              <BreakLine />
            </RightContainer>
            <CenterContainer>
              <div className='flex flex-row items-center justify-start self-stretch'>
                <span className='font-semibold text-grey-400'>
                  {convertMeterToKm(data?.distance)}
                </span>
              </div>
              <div className='gap-space-inline-8 text-text-primary flex flex-row items-center justify-start'>
                <ClockIcon src={images.Icon.ClockWatch} />
                <div className='relative leading-[16px]'>
                  {formatTimeRange({
                    startTimestamp: data?.start?.timestamp,
                    endTimestamp: data?.end?.timestamp,
                    timezone,
                  })}
                </div>
              </div>
            </CenterContainer>
          </>
        );
      default:
        return <></>;
    }
  }, []);

  if (!selectedDate) {
    return undefined;
  }
  return (
    <Container>
      {!hideSectionHeader && Header}
      {routes.map((route, index) => {
        return <Row key={index}>{renderRow(route)}</Row>;
      })}
    </Container>
  );
};

export default DeviceJourneyDetailInfo;
const Container = tw.div`w-full flex-1 flex-col text-center text-xs`;
const RightContainer = tw.div`mr-3 flex flex-col items-center justify-center self-stretch`;
const CenterContainer = tw.div`flex flex-1 flex-col items-start justify-start px-0 py-2 gap-1`;
const Row = tw.div`flex flex-row py-0`;
const BreakLine = tw.div`h-7 w-px max-w-full bg-grey-100`;
const ContainerStepLabel = tw.div`my-1 flex size-8 flex-row items-center justify-center`;
const StepLabel = tw.div`flex font-semibold leading-[16px] text-blue-200 text-[12px] w-full items-center justify-center rounded-[999px] bg-[#2F6BFF1A] p-2`;
const CompletedStatus = tw.div`my-1 flex size-8 flex-col items-center justify-center rounded-[999px] bg-green-10 p-2`;
const SpaceTop = tw.div`h-2 self-stretch`;
const ClockIcon = tw.img`mr-1 size-4`;
