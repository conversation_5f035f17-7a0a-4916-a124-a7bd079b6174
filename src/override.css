.ant-tabs-tab.ant-tabs-tab-active {
  background-color: #f0f00b !important;
  border-radius: 8px;
}
.ant-tabs-tab:hover {
  background-color: #f0f1f5 !important;
  border-radius: 8px;
}

.ant-tabs-tab.ant-tabs-tab-active div {
  color: black !important;
}
/* .ant-tabs .ant-tabs-tab {
  margin-right: 2px !important;
} */
.ant-tabs-nav-wrap {
  min-width: 180px;
}

.ant-tabs > .ant-tabs-nav {
  position: unset;
  gap: 4px;
}
.ant-tabs-nav-list {
  gap: 8px;
}

.ant-tabs-nav-list > .ant-tabs-tab {
  padding: 8px;
}

.ant-tabs .ant-tabs-tab + .ant-tabs-tab {
  margin: 0 !important;
}

.ant-tabs-nav-list > .ant-tabs-ink-bar.ant-tabs-ink-bar-animated {
  width: 0px !important;
}

/* first child */
.popover-account-list .scrollbar-container div:nth-child(1) {
  position: relative !important;
}



.ant-select-item.ant-select-item-option {
  background-color: transparent !important;
}

.ant-select-item.ant-select-item-option:hover {
  background-color: #f0f1f5 !important;
}

.rc-virtual-list-holder-inner .ant-select-item.ant-select-item-option {
  margin-bottom: 8px;
}

.rc-virtual-list-holder-inner
  .ant-select-item.ant-select-item-option:last-child {
  margin-bottom: 0;
}

.ant-modal {
  border-radius: 16px !important;
  overflow: hidden;
}

.ant-select-item-option-content {
  font-weight: 500;
}

.ant-popover-content .ant-popover-inner {
  padding: 8px !important;
}

.rr-custom-popover .ant-popover-content .ant-popover-inner {
  padding: 16px !important;
  border-radius: 12px !important;
  box-shadow: 0px 1px 8px 0px rgba(0, 0, 0, 0.12);
}
