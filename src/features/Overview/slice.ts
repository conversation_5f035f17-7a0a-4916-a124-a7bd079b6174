import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { uniqBy } from 'lodash';

import type { DeviceType } from 'types/device';

import { PageInfo } from './types';

interface AddressState {
  [key: string]: string;
}

export interface AgencyReportSummary {
  total_devices: number;
  total_users: number;
  total_inactive_devices: number;
  total_activated_devices_today: number;
  total_activated_devices_ytd: number;
  fetch_expired_devices_today: number;
  total_expired_devices_ytd: number;
  user_locations: {
    address: string;
    user_count: number;
  }[];
}

interface DeviceReportSummary {
  daily: { date: string; value: number }[];
  summary: {
    total_distance: number;
    max_speed: number;
    move_time: number;
    stop_time: number;
    lost_connection_time: number;
    report_count: number;
  };
}

interface State {
  loading: boolean;
  deviceList: DeviceType[];
  pageInfo: PageInfo;
  address: AddressState;
  deviceReportSummary: DeviceReportSummary | null;
  agencyOverview: AgencyReportSummary | null;
}

const initialState: State = {
  loading: false,
  deviceList: [],
  pageInfo: {
    page: 0,
    totalCount: 0,
    totalPages: 0,
  },
  address: {},
  deviceReportSummary: null,
  agencyOverview: {
    total_devices: 0,
    total_users: 0,
    total_inactive_devices: 0,
    total_activated_devices_today: 0,
    total_activated_devices_ytd: 0,
    fetch_expired_devices_today: 0,
    total_expired_devices_ytd: 0,
    user_locations: [],
  },
};

const slice = createSlice({
  name: 'overview',
  initialState: initialState,
  reducers: {
    getDeviceList(
      state,
      _: PayloadAction<{
        params: {
          page: number;
          search_keyword?: string;
          status?: string[];
          owner_id?: string;
          device_category?: string[];
          activated_at_from?: string;
          activated_at_to?: string;
          expired_at_from?: string;
          expired_at_to?: string;
          is_activated?: boolean;
        };
        getAddress: boolean;
      }>,
    ) {
      state.loading = true;
    },
    getDeviceListSuccess(
      state,
      action: PayloadAction<{ deviceList: DeviceType[]; pageInfo: PageInfo }>,
    ) {
      state.loading = false;
      if (action.payload.pageInfo.page === 1) {
        state.deviceList = action.payload.deviceList;
      } else {
        const combinedList = [
          ...state.deviceList,
          ...action.payload.deviceList,
        ];
        state.deviceList = uniqBy(combinedList, 'imei');
      }

      state.pageInfo = action.payload.pageInfo;
    },
    setAddress(
      state,
      action: PayloadAction<{ lng: number; lat: number; address: string }>,
    ) {
      const key = `${action.payload.lng},${action.payload.lat}`;
      state.address[key] = action.payload.address;
    },
    getDeviceReportSummary(state, _action: PayloadAction<{ imei: string }>) {
      state.loading = true;
    },
    getDeviceReportSummarySuccess(
      state,
      action: PayloadAction<DeviceReportSummary>,
    ) {
      state.loading = false;
      state.deviceReportSummary = action.payload;
    },
    getAgencyOverview(state, _action: PayloadAction<Record<string, never>>) {
      state.loading = true;
    },
    getAgencyOverviewSuccess(state, action: PayloadAction<{ data }>) {
      state.agencyOverview = action.payload.data;
      state.loading = false;
    },
  },
});

export const {
  getDeviceList,
  getDeviceListSuccess,
  setAddress,
  getDeviceReportSummary,
  getDeviceReportSummarySuccess,
  getAgencyOverview,
  getAgencyOverviewSuccess,
} = slice.actions;

export default slice.reducer;
