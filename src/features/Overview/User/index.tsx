import { useEffect, useRef, useState } from 'react';

import dayjs from 'dayjs';
import { useProfile } from 'hooks/useProfile';
import { useDispatch, useSelector } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { UserRole } from 'types/UserRoleTypes';

import {
  BodyLg,
  BodyMdBase,
  BodySm,
  BodyXl,
  FontBold,
  H4,
  TitleMd,
} from 'assets/styles';
import { RRStaticMap } from 'components';
import WithRoleAccess from 'components/WithRoleAccess';

import AppDownloadBanner from '../components/AppDownloadBanner';
import DeviceListOverview from '../components/DeviceListOverview';
import OverViewChart from '../components/OverviewChart';
import { selectDeviceReportSummary } from '../selectors';
import { getDeviceReportSummary } from '../slice';

interface OverviewProps {}
const post = { lat: 16.0688, lng: 108.2215 };

const Overview = ({}: OverviewProps) => {
  const dispatch = useDispatch();
  const { profile } = useProfile();
  const deviceReportSummary = useSelector(selectDeviceReportSummary);

  const currentDate = dayjs().format('DD/MM/YYYY');
  const currentTime = dayjs().format('HH:mm:ss');
  const currentDay = dayjs().format('dddd');

  const [_, setMapHeight] = useState<number>(0);

  const headerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    dispatch(getDeviceReportSummary({ imei: '541799318545338' }));
  }, [dispatch]);

  useEffect(() => {
    const calculateMapHeight = () => {
      const headerHeight = headerRef.current?.offsetHeight || 0;
      const chartHeight = chartRef.current?.offsetHeight || 0;
      const remainingHeight = window.innerHeight - headerHeight - chartHeight;
      setMapHeight(remainingHeight);
    };

    calculateMapHeight();
    window.addEventListener('resize', calculateMapHeight);

    return () => {
      window.removeEventListener('resize', calculateMapHeight);
    };
  }, []);

  return (
    <Container>
      <MainContainer>
        <Header ref={headerRef}>
          <OverviewSection>
            <GreetingText className={BodyLg}>
              Xin chào {profile?.full_name}
            </GreetingText>
            <OverviewText className={H4}>{t('overview.overview')}</OverviewText>
          </OverviewSection>
          <DateTimeSection>
            <LatestUpdated className={`${BodySm}`}>
              {t('overview.latestUpdate')}
            </LatestUpdated>
            <Flex>
              <DateBox className={`${TitleMd} ${FontBold}`}>
                {currentDay}
              </DateBox>
              <DateTime>
                <Date className={BodyMdBase}>{currentDate}</Date>
                <Time className={`${BodyXl} ${FontBold}`}>{currentTime}</Time>
              </DateTime>
            </Flex>
          </DateTimeSection>
        </Header>
        <div ref={chartRef}>
          <OverViewChart
            data={deviceReportSummary?.daily}
            totalMoveTime={deviceReportSummary?.summary.move_time ?? 0}
          />
        </div>
        <RRStaticMap position={post} />
      </MainContainer>
      <RightContainer>
        <DeviceListOverview />
        <FixedHeightBanner />
      </RightContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between`;
const FixedHeightBanner = tw(AppDownloadBanner)`h-[290px]]`;
const MainContainer = tw.div`p-6 grow overflow-y max-h-screen`;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col justify-between`;
const DateTimeSection = tw.div`flex flex-col items-center rounded-xl border border-grey-100`;
const DateBox = tw.span`mr-2 flex h-full items-center border-r  border-grey-100 px-3 py-3.5`;
const DateTime = tw.div`mr-2 flex flex-col py-2`;
const Date = tw.span`text-grey-400`;
const Time = tw.span``;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const Flex = tw.div`flex`;
const LatestUpdated = tw.div`w-full border-b border-grey-100 bg-grey-50 py-1 text-center text-grey-400`;

const OverviewUser = WithRoleAccess(Overview, [UserRole.User]);
export default OverviewUser;
