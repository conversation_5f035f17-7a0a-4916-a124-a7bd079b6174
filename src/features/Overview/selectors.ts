import { RootState } from 'store/rootReducer';

export const selectDeviceList = (state: RootState) => state.overview.deviceList;

export const selectDeviceImeiList = (state: RootState) =>
  state.overview.deviceList.map(device => device.imei);

export const selectPageInfo = (state: RootState) => state.overview.pageInfo;

export const selectDeviceReportSummary = (state: RootState) =>
  state.overview.deviceReportSummary;

export const selectAgencyOverview = (state: RootState) =>
  state.overview.agencyOverview;
