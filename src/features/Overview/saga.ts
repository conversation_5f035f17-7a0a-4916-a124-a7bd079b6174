import dayjs from 'dayjs';
import { SagaIterator } from 'redux-saga';
import {
  all,
  call,
  put,
  select,
  takeEvery,
  takeLatest,
} from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';
import { qsStringifyUrl } from 'services/api/utils';

import {
  converPageInfo,
  convertToDeviceList,
  parseChartData,
} from './adapters';
import {
  getAgencyOverview,
  getAgencyOverviewSuccess,
  getDeviceList,
  getDeviceListSuccess,
  getDeviceReportSummary,
  getDeviceReportSummarySuccess,
  setAddress,
} from './slice';

function* getAddressSaga(device) {
  const key = `${device.lng},${device.lat}`;
  const cachedAddress = yield select(state => state.overview.address[key]);
  if (cachedAddress) {
    return cachedAddress;
  }

  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: qsStringifyUrl({
      url: '/geocode/reverse',
      query: {
        lat: device.lat,
        lng: device.lng,
      },
    }),
  });

  if (response.success) {
    const address = response.response.data.geocode_result.full_address;
    yield put(setAddress({ lng: device.lng, lat: device.lat, address }));
    return address;
  }
  return '';
}

function* getDeviceListSaga(
  _action: ReturnType<typeof getDeviceList>,
): SagaIterator {
  const params = _action.payload.params;
  const getAddress = _action.payload.getAddress;
  const query: Record<string, string> = {
    page: params.page + '',
  };

  if (params.owner_id) query.owner_id = params.owner_id;
  if (params.search_keyword) query.search_keyword = params.search_keyword;
  if (params.status?.length) query.statuses = params.status.join(',');
  if (params.device_category?.length)
    query.device_category = params.device_category.join(',');
  if (params.activated_at_from)
    query.activated_at_from = params.activated_at_from;
  if (params.activated_at_to) query.activated_at_to = params.activated_at_to;
  if (params.expired_at_from) query.expired_at_from = params.expired_at_from;
  if (params.expired_at_to) query.expired_at_to = params.expired_at_to;

  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: qsStringifyUrl({
      url: '/devices',
      query,
    }),
  });

  if (response.success) {
    const deviceList = convertToDeviceList(response.response.data);
    const pageInfo = converPageInfo(response.response.data);
    let updatedDeviceList = deviceList;
    if (getAddress) {
      updatedDeviceList = yield all(
        deviceList?.map(device =>
          call(function* () {
            const address = yield call(getAddressSaga, device);
            return { ...device, address };
          }),
        ),
      );
    }

    const payload = {
      deviceList: updatedDeviceList,
      pageInfo,
    };

    yield put(getDeviceListSuccess(payload));
  }
}

function* getDeviceReportSummarySaga(
  _action: ReturnType<typeof getDeviceReportSummary>,
): SagaIterator {
  const imei = _action.payload.imei;
  const endDate = dayjs().format('YYYY-MM-DD');
  const startDate = dayjs().subtract(30, 'days').format('YYYY-MM-DD');
  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: `/device_reports/summary?imei=${imei}&start_date=${startDate}&end_date=${endDate}`,
  });

  if (response.success) {
    const summary = parseChartData(response.response.data.daily);
    yield put(
      getDeviceReportSummarySuccess({
        daily: summary,
        summary: response.response.data.summary,
      }),
    );
  }
}

function* getAgencyReportSummarySaga(
  _action: ReturnType<typeof getAgencyOverview>,
): SagaIterator {
  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: `/distributor_reports/summary`,
  });

  if (response.success) {
    yield put(
      getAgencyOverviewSuccess({
        data: response.response.data,
      }),
    );
  }
}

export function* watchOverviewSaga(): SagaIterator {
  yield takeLatest(getDeviceList.type, getDeviceListSaga);
  yield takeEvery(getDeviceReportSummary.type, getDeviceReportSummarySaga);
  yield takeEvery(getAgencyOverview.type, getAgencyReportSummarySaga);
}
