import { useEffect } from 'react';

import { Flex } from 'antd';
import dayjs from 'dayjs';
import { useProfile } from 'hooks/useProfile';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { UserRole } from 'types/UserRoleTypes';

import images from 'assets/images';
import {
  BodyLg,
  BodyMdBase,
  BodySm,
  BodyXl,
  FontBold,
  H4,
  TitleMd,
} from 'assets/styles';
import { Icon, RRStaticMap } from 'components';
import { LinkButton } from 'components/Button';
import { PinType } from 'components/RRStaticMap';
import WithRoleAccess from 'components/WithRoleAccess';

import DeviceListOverview from '../components/DeviceListOverview';
import DistrictProgressStats from '../components/DistrictProgressStats';
import { selectAgencyOverview } from '../selectors';
import { getAgencyOverview } from '../slice';

interface OverviewProps {}
const post = { lat: 16.0688, lng: 108.2215 };

const Overview = ({}: OverviewProps) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { profile } = useProfile();
  const agencySum = useSelector(selectAgencyOverview);
  const currentDate = dayjs().format('DD/MM/YYYY');
  const currentTime = dayjs().format('HH:mm:ss');
  const currentDay = dayjs().format('dddd');

  const handleRedirectToAcc = () => {
    navigate(`/business/accounts`);
  };

  useEffect(() => {
    dispatch(getAgencyOverview({}));
  }, []);

  return (
    <Container>
      <MainContainer vertical>
        <Header>
          <OverviewSection>
            <GreetingText className={BodyLg}>
              {' '}
              Xin chào {profile?.full_name}
            </GreetingText>
            <OverviewText className={H4}>{t('overview.overview')}</OverviewText>
          </OverviewSection>
          <DateTimeSection>
            <LatestUpdated className={`${BodySm}`}>
              {t('overview.latestUpdate')}
            </LatestUpdated>
            <Flex>
              <DateBox className={`${TitleMd} ${FontBold}`}>
                {currentDay}
              </DateBox>
              <DateTime>
                <Date className={BodyMdBase}>{currentDate}</Date>
                <Time className={`${BodyXl} ${FontBold}`}>{currentTime}</Time>
              </DateTime>
            </Flex>
          </DateTimeSection>
        </Header>
        <Flex align='center' justify='space-between'>
          <SectionHeader>{t('overview.statistic')}</SectionHeader>
          <LinkButton
            onClick={handleRedirectToAcc}
            size='small'
            className='my-3 h-6 text-end'
          >
            {t('overview.manageAccountAndDevice')}
          </LinkButton>
        </Flex>
        <Flex justify='space-evenly'>
          <RowContainerGap3>
            <IconContainer>
              <Icon src={images.Icon.DeviceTime} />
            </IconContainer>
            <ColumnGap2>
              <MainText>{t('overview.activeDate')}</MainText>
              <Flex align='baseline'>
                <BoldText>{agencySum?.total_activated_devices_today}</BoldText>
                <SubText>{t('overview.device')}</SubText>
                <MetricContainer>
                  <Icon src={images.Icon.Up} className='size-3' />
                  <GreenText className='flex flex-1'>
                    {agencySum?.total_activated_devices_ytd
                      ? (agencySum?.total_activated_devices_today /
                          agencySum?.total_activated_devices_ytd) *
                        100
                      : 0}
                    %
                  </GreenText>
                </MetricContainer>
              </Flex>
            </ColumnGap2>
          </RowContainerGap3>
          <VeticalDivider />
          <RowContainerGap3>
            <IconContainer>
              <Icon src={images.Icon.DeviceSignalSquare} />
            </IconContainer>
            <ColumnGap2>
              <MainText>{t('overview.inventory')}</MainText>
              <Flex align='baseline'>
                <BoldText>
                  {agencySum?.total_inactive_devices}/{agencySum?.total_devices}
                </BoldText>
                <SubText>{t('overview.device')}</SubText>
              </Flex>
            </ColumnGap2>
          </RowContainerGap3>
          <VeticalDivider />
          <RowContainerGap3>
            <IconContainer>
              <Icon src={images.Icon.CalendarMinus} />
            </IconContainer>
            <ColumnGap2>
              <MainText>{t('overview.expiredService')}</MainText>
              <Flex align='baseline'>
                <BoldText>{agencySum?.fetch_expired_devices_today}</BoldText>
                <SubText>{t('overview.device')}</SubText>
                <MetricContainer>
                  <Icon src={images.Icon.Up} className='size-3' />
                  <GreenText className='flex flex-1'>
                    {agencySum?.fetch_expired_devices_today
                      ? (agencySum?.fetch_expired_devices_today /
                          agencySum?.total_expired_devices_ytd) *
                        100
                      : 0}
                    %
                  </GreenText>
                </MetricContainer>
              </Flex>
            </ColumnGap2>
          </RowContainerGap3>
        </Flex>
        <SectionHeader>Địa điểm lắp đặt</SectionHeader>

        <RRStaticMap
          height='100%'
          showLabel={false}
          pinType={PinType.Location}
          position={post}
          statisticComponent={
            <SummaryMapFrame>
              <DistrictProgressStats data={agencySum} />
            </SummaryMapFrame>
          }
        />
      </MainContainer>
      <RightContainer>
        <DeviceListOverview />
      </RightContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex flex-col h-screen border-l border-grey-100 justify-between`;
const MainContainer = tw(Flex)`p-6 grow`;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col justify-between`;
const DateTimeSection = tw.div`flex flex-col items-center rounded-xl border border-grey-100`;
const DateBox = tw.span`mr-2 flex h-full items-center border-r  border-grey-100 px-3 py-3.5`;
const DateTime = tw.div`mr-2 flex flex-col py-2`;
const Date = tw.span`text-grey-400`;
const Time = tw.span``;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const LatestUpdated = tw.div`w-full border-b border-grey-100 bg-grey-50 py-1 text-center text-grey-400`;
const SectionHeader = tw.span`text-lg font-bold my-4`;
const RowContainerGap3 = tw.div`flex flex-row gap-3 items-center`;
const ColumnGap2 = tw.div`inline-flex flex-1 flex-col gap-2`;
const MainText = tw.div`text-grey-600 text-sm font-medium font-['Inter'] leading-normal`;
const SubText = tw.div`text-grey-600 text-xs font-medium font-['Inter'] leading-none ml-1`;
const MetricContainer = tw.div`gap-0.5 font-['Inter'] text-xs font-medium leading-none items-center ml-2 inline-flex flex-1`;
const GreenText = tw.span`text-green-200`;
const BoldText = tw.span`text-black text-xl font-bold font-['Inter'] leading-7`;
const IconContainer = tw.div`items-center justify-center rounded-full bg-grey-50 p-3`;
const VeticalDivider = tw.div`flex w-px bg-grey-100 mx-1`;
const SummaryMapFrame = tw.div`px-6 py-4 bg-white-1000 rounded-2xl shadow justify-start inline-flex flex-row w-full`;

const OverviewAgency = WithRoleAccess(Overview, [UserRole.Agency]);
export default OverviewAgency;
