import { useProfile } from 'hooks/useProfile';

import DeviceListPanel from 'features/DeviceBusiness/components/DeviceListPanel';
import { MODAL_TYPES } from 'features/DeviceBusiness/constants';
import { useDeviceState } from 'features/DeviceBusiness/hooks';

import { DrawerDeviceDetail } from 'components/DeviceModal';

const DeviceListOverview = () => {
  const { profile } = useProfile();
  const {
    state,
    openModal,
    closeModal,
    updateState,
    onDeviceSelect,
    onSelectAccount,
    onChangeBreadcrumb,
  } = useDeviceState();
  const { modals, selectedDevice, multipleSelect } = state;

  const handleOpenDrawer = (data: any) => {
    openModal(MODAL_TYPES.DRAWER_DETAILS);
    onDeviceSelect(data);
  };

  return (
    <>
      <DeviceListPanel
        ignoreFirstSelect
        enableViewAllDevice
        enableAdvanceFilter
        enableAddDevice={false}
        enableSelectAccount={false}
        enableMultipleSelect={false}
        className='p-6'
        selectedAccount={profile as any}
        multipleSelect={multipleSelect}
        profile={profile}
        selectedDevice={selectedDevice}
        onDeviceSelect={handleOpenDrawer}
        openModal={openModal}
        updateState={updateState}
        onSelectAccount={onSelectAccount}
        onChangeBreadcrumb={onChangeBreadcrumb}
      />
      <DrawerDeviceDetail
        visible={modals[MODAL_TYPES.DRAWER_DETAILS]}
        onClose={() => closeModal(MODAL_TYPES.DRAWER_DETAILS)}
        selectedImei={selectedDevice?.imei ?? ''}
      />
    </>
  );
};

export default DeviceListOverview;
