import { useState } from 'react';

import { Progress } from 'antd';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { Colors } from 'assets/styles';
import { Icon } from 'components';

import { AgencyReportSummary } from '../slice';

interface District {
  name: string;
  percentage: number;
}
const districts: District[] = [
  { name: 'Quận Gò Vấp', percentage: 50 },
  { name: 'Quận Tân Bình', percentage: 75 },
  { name: 'Quận 1', percentage: 62 },
  { name: 'Quận 7', percentage: 38 },
  { name: 'Quận 3', percentage: 45 },
  { name: 'Quận 10', percentage: 80 },
];
type Props = {
  data: AgencyReportSummary | null;
};
const DistrictProgressStats: React.FC<Props> = ({ data }: Props) => {
  const [startIndex, setStartIndex] = useState(0);
  const itemsToShow = 4;
  const canScrollLeft = startIndex > 0;
  const canScrollRight = startIndex + itemsToShow < districts.length;

  const handlePrevious = () => {
    if (canScrollLeft) {
      setStartIndex(startIndex - 1);
    }
  };

  const handleNext = () => {
    if (canScrollRight) {
      setStartIndex(startIndex + 1);
    }
  };

  return (
    <Container>
      <LeftSummaryMapFrame>
        <SummaryLabel>{data?.total_users}</SummaryLabel>
        <SubTextSummaryLabel>{t('overview.numOfAccount')}</SubTextSummaryLabel>
      </LeftSummaryMapFrame>
      <VeticalDivider />
      <RightSummaryMapFrame>
        <ActionButton
          onClick={handlePrevious}
          className={`left-0 ${
            canScrollLeft
              ? 'hover:bg-grey-100'
              : 'cursor-not-allowed opacity-50'
          }`}
          disabled={!canScrollLeft}
        >
          <Icon src={images.Icon.ChevronLeft} />
        </ActionButton>

        <DistrictContainer>
          <DistrictList
            style={{
              transform: `translateX(-${startIndex * (100 / itemsToShow)}%)`,
              width: `${(districts.length / itemsToShow) * 100}%`,
            }}
          >
            {data?.user_locations?.map((district, index) => (
              <DistrictItem
                key={index}
                style={{ width: `${100 / itemsToShow}%` }}
              >
                <Progress
                  type='circle'
                  percent={parseFloat(
                    ((district?.user_count / data.total_users) * 100).toFixed(
                      1,
                    ),
                  )}
                  strokeColor={Colors.green[100]}
                  strokeWidth={8}
                  size={52}
                />
                <DistrictLabel>{district.address}</DistrictLabel>
              </DistrictItem>
            ))}
          </DistrictList>
        </DistrictContainer>

        <ActionButton
          onClick={handleNext}
          className={`right-0 ${
            canScrollRight
              ? 'hover:bg-grey-100'
              : 'cursor-not-allowed opacity-50'
          }`}
          disabled={!canScrollRight}
        >
          <Icon src={images.Icon.ChevronRight} />
        </ActionButton>
      </RightSummaryMapFrame>
    </Container>
  );
};
const Container = tw.div`flex flex-1 flex-row gap-6`;
const LeftSummaryMapFrame = tw.div`inline-flex flex-col items-center justify-center gap-2`;
const SummaryLabel = tw.span`font-['Inter'] text-3xl font-bold italic leading-10 text-black-1000 `;
const SubTextSummaryLabel = tw.span`text-center font-['Inter'] text-xs font-medium leading-none text-grey-600`;
const RightSummaryMapFrame = tw.div`relative flex flex-1 items-center`;
const VeticalDivider = tw.div`flex w-px bg-grey-100`;
const ActionButton = tw.button`bg-white absolute z-10 rounded-lg border-grey-100 border p-2 shadow-sm `;
const DistrictContainer = tw.div`mx-8 flex flex-1 overflow-hidden`;
const DistrictList = tw.div`flex transition-transform duration-300 ease-in-out`;
const DistrictItem = tw.div`flex flex-none flex-col items-center px-2`;
const DistrictLabel = tw.span`text-grey-600 mt-2 text-center text-sm`;
export default DistrictProgressStats;
