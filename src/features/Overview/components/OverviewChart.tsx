import { ResponsiveBar } from '@nivo/bar';
import { BodyMdBase, Colors, FontBold, TitleMd } from 'assets/styles';
import { t } from 'i18next';
import tw from 'tailwind-styled-components';

type DataPoint = {
  date: string;
  value: number;
};

const generateTickValues = (data: DataPoint[], step: number): string[] => {
  const tickValues: string[] = [];
  for (let i = 0; i < data.length; i += step) {
    tickValues.push(data[i].date);
  }
  return tickValues;
};

interface OverViewChartProps {
  data: { date: string; value: number }[] | undefined;
  totalMoveTime: number;
}

const OverViewChart = ({ data = [], totalMoveTime }: OverViewChartProps) => {
  const tickValues = generateTickValues(data, 6);
  const hours = Math.floor(totalMoveTime / 3600); // Total hours
  const minutes = Math.floor((totalMoveTime % 3600) / 60); // Remaining minutes
  return (
    <Container>
      <Header>
        <div>
          <Label className={BodyMdBase}>{t('overview.movingTime')}</Label>
          <Value className={`${TitleMd} ${FontBold}`}>
            {hours} giờ {minutes} phút
          </Value>
        </div>
        <div>
          <Label className={BodyMdBase}>{t('overview.data')} </Label>
          <Value className={`${BodyMdBase} ${FontBold}`}>
            30 ngày gần nhất
          </Value>
        </div>
      </Header>
      <ChartContainer>
        <ResponsiveBar
          data={data}
          keys={['value']}
          indexBy="date"
          margin={{ top: 20, right: 0, bottom: 50, left: 0 }}
          padding={0.7}
          colors={Colors.blue[200]}
          enableLabel={false}
          borderRadius={5}
          theme={{
            axis: {
              ticks: {
                text: {
                  fill: Colors.grey[600],
                },
              },
            },
          }}
          axisLeft={null}
          axisBottom={{
            tickValues: tickValues,
          }}
        />
      </ChartContainer>
    </Container>
  );
};

export default OverViewChart;

const Label = tw.div`text-grey-600`;
const Value = tw.div`text-black-1000`;
const Container = tw.div`mt-6 h-[400px] rounded-lg border border-grey-100 p-6`;
const Header = tw.div`flex justify-between`;
const ChartContainer = tw.div<{
  collapsed?: boolean;
}>` h-full ${p =>
  p.collapsed ? `w-[calc(100%-240px)]` : 'w-[calc(100%-10px)]'}`;
