import { useMemo } from 'react';

import { useProfile } from 'hooks/useProfile';
import { useLocation } from 'react-router-dom';

import { UserProfile, UserRole } from 'types/UserRoleTypes';

import OverviewAgency from './Agency';
import OverviewUser from './User';

interface OverviewProps {}

const Overview = ({}: OverviewProps) => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const { profile } = useProfile();

  const roleParam = searchParams.get('role');

  const userProfile: UserProfile = useMemo(
    () => ({
      id: '1',
      role:
        profile?.role_type === 'distributor' ? UserRole.Agency : UserRole.User,
    }),
    [roleParam, profile],
  );
  return (
    <>
      <OverviewUser userProfile={userProfile} />
      <OverviewAgency userProfile={userProfile} />
    </>
  );
};

export default Overview;
