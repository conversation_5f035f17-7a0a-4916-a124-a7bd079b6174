import React, { useImperativeHandle } from 'react';

import { DeviceOptions, ServicePackageOptions } from 'constants/device';

import { RRCalendar } from 'components';
import PrimaryButton from 'components/Button/PrimaryButton';
import { RRMultipleSelect } from 'components/FormField';

import { useAdvancedFilter } from './hooks/useAdvancedFilter';
import { AdvancedFilterProps, AdvancedFilterRef } from './types';

// Theme constants

const getCalendarInputClass = (hasValue: boolean) => {
  return hasValue ? `bg-brand-300 px-2` : 'px-2';
};

const AdvancedFilter = React.forwardRef<AdvancedFilterRef, AdvancedFilterProps>(
  ({ className, onFilterSelect }, ref) => {
    const {
      filterState,
      updateServicePackage,
      updateTypeOfDevice,
      updateInventory,
      updateActiveDateRange,
      updateExpiredDateRange,
      reset,
    } = useAdvancedFilter({ onFilterSelect });

    const {
      servicePackage,
      typeOfDevice,
      inventory,
      activeDateRange,
      expiredDateRange,
    } = filterState;

    useImperativeHandle(ref, () => ({
      reset,
    }));

    // Computed values for layout and styling
    const hasActiveDate = !!activeDateRange.end;
    const hasExpiredDate = !!expiredDateRange.end;
    const shouldUseColumnLayout = hasActiveDate || hasExpiredDate;

    return (
      <div className={`w-full space-y-2 ${className || ''}`}>
        <div className='flex items-center gap-2'>
          <div className='flex-auto'>
            <RRMultipleSelect
              placement='bottomLeft'
              options={DeviceOptions}
              placeholder='Loại thiết bị'
              className={getCalendarInputClass(typeOfDevice?.length > 0)}
              value={typeOfDevice}
              onChange={updateTypeOfDevice}
            />
          </div>
          <div className='flex-auto'>
            <RRMultipleSelect
              placement='bottomLeft'
              value={servicePackage}
              options={ServicePackageOptions}
              placeholder='Gói dịch vụ'
              className={getCalendarInputClass(servicePackage?.length > 0)}
              onChange={updateServicePackage}
            />
          </div>
          <div className='flex-auto'>
            <PrimaryButton
              className={`w-full flex-auto font-normal hover:border-grey-100 hover:bg-grey-100 focus:ring-0 focus:ring-offset-0 ${
                !inventory ? 'border-grey-50 bg-grey-50' : ''
              }`}
              onClick={() => updateInventory(!inventory)}
            >
              Tồn kho
            </PrimaryButton>
          </div>
        </div>

        <div
          className={`flex gap-2 ${
            shouldUseColumnLayout ? 'flex-col' : 'flex-row'
          }`}
        >
          <div className={hasActiveDate ? 'w-full' : 'w-1/2'}>
            <RRCalendar
              placeholder='Ngày kích hoạt'
              startDate={activeDateRange.start}
              endDate={activeDateRange.end}
              classNameInput={getCalendarInputClass(hasActiveDate)}
              minDateTime={undefined}
              maxDateTime={new Date()}
              onSelect={updateActiveDateRange}
            />
            <div></div>
          </div>

          <div className={hasExpiredDate ? 'w-full' : 'w-1/2'}>
            <RRCalendar
              placeholder='Ngày hết hạn'
              startDate={expiredDateRange.start}
              endDate={expiredDateRange.end}
              classNameInput={getCalendarInputClass(hasExpiredDate)}
              onSelect={updateExpiredDateRange}
            />
            <div></div>
          </div>
        </div>
      </div>
    );
  },
);

AdvancedFilter.displayName = 'AdvancedFilter';

export default AdvancedFilter;
