import { useCallback, useEffect } from 'react';

import { Tabs } from 'antd';
import TabPane from 'antd/es/tabs/TabPane';
import { FormProvider, useForm } from 'react-hook-form';

import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';
import { LinkButton, PrimaryButton } from 'components/Button';
import {
  ConfigForm,
  DeviceInfoForm,
  StatusForm,
} from 'components/DeviceDetail';
import EmptyChoice from 'components/EmptyChoice';

import { MODAL_TYPES } from '../constants';
import { useDeviceDetail, useDeviceMutations } from '../hooks';

interface DeviceDetailTabsProps {
  selectedImei: string;
  openModal: (modalType: string, modalProps?: any) => void;
  onSelectedMultipleDevice: (devices: any[]) => void;
}

const DeviceDetailTabs = ({
  selectedImei,
  openModal,
  onSelectedMultipleDevice,
}: DeviceDetailTabsProps) => {
  const { data: deviceDetail } = useDeviceDetail({
    imei: selectedImei,
  });

  const { updateDevice } = useDeviceMutations();

  const methods = useForm({
    defaultValues: {},
  });
  const { formState, handleSubmit } = methods;

  const onSubmit = useCallback(
    data => {
      const payload = {
        // info
        transport_department_id: data?.transport_department_id,
        transport_type: data?.transport_type,
        is_allow_data_transport: data?.is_allow_data_transport,
        device_name: data?.device_name,
        device_plate_number: data?.device_plate_number,
        device_ccid: data?.device_ccid,
        device_sim_number: data?.device_sim_number,
        device_category: data?.device_category,
        service_package: data?.service_package,
        activated_at: data?.activated_at,
        expired_at: data?.expired_at,

        // status
        not_turn_off_the_ignition_time: data?.not_turn_off_the_ignition_time,
        max_allowable_speed: data?.max_allowable_speed,
        device_pin: data?.device_pin,

        // Config
        gprs_interval: data?.gprs_interval,
        min_speed: data?.min_speed,
        stop_time: data?.stop_time,
        timezone: data?.timezone,
        sensor_type: data?.sensor_type,
        stop_distance: data?.stop_distance,
      };
      updateDevice.mutate({
        imei: selectedImei,
        deviceInfo: payload,
      });
    },
    [selectedImei, updateDevice],
  );

  const handleOpenTransferModal = useCallback(() => {
    onSelectedMultipleDevice([deviceDetail]);
    setTimeout(() => {
      openModal(MODAL_TYPES.MOVE_DEVICE);
    }, 0);
  }, [openModal, deviceDetail, selectedImei]);

  useEffect(() => {
    if (deviceDetail) {
      methods.reset({
        ...deviceDetail,
        device_sim_number: deviceDetail?.device_sim_number || '-',
        engine: deviceDetail?.engine === '1' ? 'Bật' : 'Tắt',
        ignitionLock: deviceDetail?.ignitionLock === '1' ? 'Bật' : 'Tắt',
      });
    }
  }, [deviceDetail, methods]);

  if (!selectedImei) {
    return <EmptyChoice content='Chọn thiết bị để xem thông tin' />;
  }

  return (
    <div>
      <div className='mb-3 flex items-center justify-between'>
        <LinkButton
          onClick={() => openModal(MODAL_TYPES.DEVICE_LOG)}
          size='small'
          iconPosition='left'
          className='h-6'
          icon={<Icon src={images.Icon.Calendar} />}
        >
          {t('deviceDetail.deviceLog')}
        </LinkButton>

        <LinkButton
          onClick={handleOpenTransferModal}
          size='small'
          iconPosition='left'
          className='h-6'
          icon={<Icon src={images.Icon.ChangeDevice} />}
        >
          {t('deviceDetail.transferDevice')}
        </LinkButton>
      </div>
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)} className='relative'>
          <Tabs
            className='size-full'
            style={{ height: '100%' }}
            tabPosition='top'
          >
            <TabPane tab={'Thông tin tổng quan'} key='1'>
              <DeviceInfoForm />
            </TabPane>
            <TabPane tab={'Trạng Thái'} key='2'>
              <StatusForm />
            </TabPane>
            <TabPane tab={'Cấu hình'} key='3'>
              <ConfigForm />
            </TabPane>
          </Tabs>
          <div className='absolute inset-x-[24px] bottom-[24px]'>
            <PrimaryButton
              htmlType='submit'
              className='w-full'
              disabled={
                updateDevice.isPending ||
                !formState.isDirty ||
                !formState.isValid
              }
              loading={updateDevice.isPending}
            >
              {t('deviceDetail.saveInfo')}
            </PrimaryButton>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default DeviceDetailTabs;
