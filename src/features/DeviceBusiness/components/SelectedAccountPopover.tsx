import { useCallback, useEffect, useState } from 'react';

import { Popover } from 'antd';
import { useRole } from 'hooks';

import { addUsersToChildren } from 'features/AccountBusiness/adapters';
import AccountList from 'features/AccountBusiness/components/AccountList';
import AddAccountModal from 'features/AccountBusiness/components/AddAccountModal';
import {
  useAccountData,
  useAccountMutations,
  useAccountState,
  useSearchAccounts,
} from 'features/AccountBusiness/hooks';
import {
  createBreadcrumbFromItem,
  findAccountById,
} from 'features/AccountBusiness/utils/accountHelpers';
import type { AccountBreadCrumbItem } from 'features/DeviceBusiness/types';
import { Account } from 'features/Profile/types';
import { t } from 'i18next';

import images from 'assets/images';
import { Icon } from 'components';
import { RRInput } from 'components/FormField';
import { CollapseItemData } from 'components/RRCollapseTable';

interface Props {
  profile: Account;
  distributorParamId: string | null;
  selectedAccount?: CollapseItemData | null;
  accountBreadcrumb?: AccountBreadCrumbItem[];
  onChange: (account: CollapseItemData) => void;
  onUpdateAccountBreadcrumb: (breadcrumb: AccountBreadCrumbItem[]) => void;
}

const SelectedAccountPopover = (props: Props) => {
  const {
    distributorParamId,
    profile,
    selectedAccount,
    accountBreadcrumb,
    onChange,
    onUpdateAccountBreadcrumb,
  } = props || {};
  const {
    state,
    updateState,
    toggleModal,
    resetSelection,
    changeExpandAccountIds,
  } = useAccountState();

  const { modals, modalProps } = state;

  const [isOpen, setIsOpen] = useState(false);

  const { roles } = useRole();
  const {
    accounts: {
      data: profileData,
      status: profileStatus,
      refetch: refetchProfileData,
    } = {
      data: [],
      profileStatus,
    },
  } = useAccountData(profile);

  const searchAccounts = useSearchAccounts(state.searchTerm, profile);
  const mutations = useAccountMutations(profile);

  const searchData = searchAccounts.data || [];
  const accountData = state.accountData || [];

  const handleSelectAccount = useCallback(
    selectedItem => {
      if (!selectedItem) {
        resetSelection();
        return;
      }

      updateState({
        selectedAccount: selectedItem,
      });
      onChange(selectedItem);
      setIsOpen(false);
    },
    [state.selectedAccount, accountData, resetSelection, updateState],
  );

  const handleSearch = useCallback(
    (value: string) => {
      updateState({ searchTerm: value });
    },
    [updateState],
  );

  const handleExpandAccount = useCallback(
    (id: string) => {
      const targetAccount = findAccountById(state.accountData, id?.toString());

      if (!targetAccount) {
        return;
      }

      if (targetAccount?.is_end_user) {
        return;
      }

      // collapse
      if (state.expandAccountIds.includes(id)) {
        changeExpandAccountIds(state.expandAccountIds.filter(i => i !== id));
        return;
      }
      // expand
      changeExpandAccountIds([...state.expandAccountIds, id]);

      if (
        targetAccount?.children_count &&
        targetAccount?.children_count > 0 &&
        targetAccount?.children?.length === 0
      ) {
        mutations.expandAccount.mutate(
          { id },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: id,
                newUsers: res.users || [],
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
                roles,
              });

              //

              updateState({
                accountData: newAccountData,
              });
            },
            onError: () => {
              changeExpandAccountIds(
                state.expandAccountIds.filter(i => i !== id),
              );
            },
          },
        );
      }
    },
    [
      findAccountById,
      mutations.expandAccount,
      state.expandAccountIds,
      state.accountData,
      roles,
      changeExpandAccountIds,
      updateState,
    ],
  );

  const handleLoadMore = useCallback(
    (data: CollapseItemData) => {
      const {
        id,
        pagination: { total_pages, page } = {
          total_pages: 1,
          page: 1,
        },
      } = data || {};
      if (total_pages && page && total_pages > page) {
        mutations.expandAccount.mutate(
          {
            id,
            page: page + 1,
          },
          {
            onSuccess: res => {
              const newAccountData = addUsersToChildren({
                accounts: state.accountData,
                targetId: data.id,
                newUsers: res.users || [],
                roles,
                pagination: {
                  total_pages: res.total_pages,
                  page: res.page,
                  total_count: res.total_count,
                },
              });

              updateState({
                accountData: newAccountData,
              });
            },
          },
        );
      }
    },
    [mutations.expandAccount, state.accountData, roles, updateState],
  );

  const handleChangeBreadcrumb = useCallback(
    item => {
      const breadcrumb = createBreadcrumbFromItem(item, accountData);
      onUpdateAccountBreadcrumb(breadcrumb);
    },
    [accountData, updateState],
  );

  const handleRefreshExpandAccount = useCallback(() => {
    refetchProfileData?.();
  }, []);

  useEffect(() => {
    // when update first level tree, reload tree.
    if (profileData && profileData.length > 0 && profileStatus === 'success') {
      updateState({
        accountData: [...profileData],
        expandAccountIds: [profileData?.[0]?.id],
      });
      if (distributorParamId) {
        const selectedAccount = findAccountById(
          profileData,
          distributorParamId,
        );
        if (selectedAccount) {
          onChange(selectedAccount);
        }
      }
    }
  }, [distributorParamId, profileStatus, profileData]);

  useEffect(() => {
    if (
      !selectedAccount ||
      !selectedAccount.id ||
      !accountBreadcrumb ||
      accountBreadcrumb?.length > 0
    ) {
      return;
    }

    const breadcrumb = createBreadcrumbFromItem(selectedAccount, accountData);
    onUpdateAccountBreadcrumb(breadcrumb);
  }, [accountBreadcrumb, selectedAccount, accountData]);

  return (
    <>
      <Popover
        open={isOpen}
        onOpenChange={setIsOpen}
        overlayClassName='rr-custom-popover'
        content={
          <div className='flex h-fit max-h-[calc(100vh-120px)] w-[328px] flex-col gap-3'>
            <span className='text-sm font-bold'>Danh sách tài khoản</span>
            <RRInput
              type='text'
              value={state.searchTerm}
              prefixIcon={images.Icon.SearchLoupe}
              placeholder={t('map.filterPlaceholder')}
              showClearButton={true}
              onChange={e => handleSearch(e.target.value)}
              onClear={() => handleSearch('')}
            />
            <div className='h-full w-[calc(100%+14px)] overflow-auto'>
              <div className='h-full w-[calc(100%-14px)] overflow-x-hidden'>
                <AccountList
                  className='popover-account-list '
                  enableMultipleSelect={false}
                  expandAccountIds={state.expandAccountIds}
                  searchTerm={state.searchTerm}
                  searchData={searchData}
                  accountData={accountData}
                  selectedAccount={state.selectedAccount}
                  multipleSelect={state.multipleSelect}
                  onSelectAccount={handleSelectAccount}
                  onOpenMoveModal={() => toggleModal('moveAccount', true)}
                  onOpenAddAccountModal={data => {
                    toggleModal('addAccount', true, data);
                    setIsOpen(false);
                  }}
                  onExpandAccount={handleExpandAccount}
                  onLoadMore={handleLoadMore}
                  onChangeBreadcrumb={handleChangeBreadcrumb}
                  onSelectedMultipleIds={() => {}}
                  onToggleMultipleSelect={() => {}}
                />
              </div>
            </div>
          </div>
        }
        trigger='click'
        placement='rightBottom'
        arrow={false}
      >
        <Icon className='cursor-pointer' src={images.Icon.FilterMenu} />
      </Popover>
      <AddAccountModal
        visible={modals.addAccount}
        modalProps={modalProps?.addAccount}
        refreshExpandParentAccount={handleRefreshExpandAccount}
        onClose={() => toggleModal('addAccount', false)}
      />
    </>
  );
};

export default SelectedAccountPopover;
