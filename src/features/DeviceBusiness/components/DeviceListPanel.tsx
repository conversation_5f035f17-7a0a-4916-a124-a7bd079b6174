import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Button, Checkbox } from 'antd';
import { DeviceStatusOption } from 'constants/device';
import { Dayjs } from 'dayjs';
import useDebounce from 'hooks/useDebounce';
import { useNavigate } from 'react-router-dom';
import { twMerge } from 'tailwind-merge';
import tw from 'tailwind-styled-components';

import AdvancedFilter from 'features/DeviceBusiness/components/AdvancedFilter';
import type { FilterState } from 'features/DeviceBusiness/components/AdvancedFilter/types';
import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdExtend, BodySm, FontBold, FontMedium } from 'assets/styles';
import { Icon } from 'components';
import { LinkButton } from 'components/Button';
import DeviceList from 'components/DeviceList';
import EmptyPanel from 'components/EmptyPanel';
import { RRInput } from 'components/FormField';
import { CollapseItemData } from 'components/RRCollapseTable';

import { MODAL_TYPES } from '../constants';
import { useDeviceData } from '../hooks';
import { useDeviceCountByStatus } from '../hooks/useDeviceData';
import type { AccountBreadCrumbItem } from '../types';
import AllDeviceSelect from './AllDeviceSelect';
import SelectedAccountPopover from './SelectedAccountPopover';

interface DeviceListPanelProps {
  isMap?: boolean;
  ignoreFirstSelect?: boolean;
  enableSelectAccount: boolean;
  enableMultipleSelect: boolean;
  enableAddDevice: boolean;
  enableViewAllDevice: boolean;
  enableAdvanceFilter: boolean;
  className?: string;
  profile: any;
  selectedDevice: any;
  selectedAccount?: CollapseItemData | null;
  multipleSelect: { isActive: boolean; multipleDevice: any[] };
  accountBreadcrumb?: AccountBreadCrumbItem[];
  openModal: (modalType: string, modalProps?: any) => void;
  onDeviceSelect: (device: any) => void;
  updateState: (data: any) => void;
  onSelectAccount: (account: CollapseItemData) => void;
  onChangeBreadcrumb: (breadcrumb: AccountBreadCrumbItem[]) => void;
  onUpdateDeviceList?: (data: any) => void;
}

const DEFAULT_FILTER_STATE = {
  servicePackage: [],
  typeOfDevice: [],
  inventory: null,
  activeDateRange: { start: undefined, end: undefined },
  expiredDateRange: { start: undefined, end: undefined },
  isActive: false,
  lastedUpdated: null,
};

const getDateString = (date?: Dayjs) => {
  if (!date || !date.isValid()) return '';
  return date.format('YYYY-MM-DD');
};

const DeviceListPanel = ({
  isMap,
  ignoreFirstSelect,
  enableSelectAccount,
  enableMultipleSelect,
  enableAddDevice,
  enableViewAllDevice,
  enableAdvanceFilter,
  className = '',
  profile,
  multipleSelect,
  selectedDevice,
  selectedAccount,
  accountBreadcrumb,
  onDeviceSelect,
  openModal,
  updateState,
  onSelectAccount,
  onChangeBreadcrumb,
  onUpdateDeviceList,
}: DeviceListPanelProps) => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState('');
  const [advancedFilters, setAdvancedFilters] =
    useState<FilterState>(DEFAULT_FILTER_STATE);
  const [filterStatus, setFilterStatus] = useState<string[]>(
    DeviceStatusOption.map(item => item.value),
  );
  const [deviceList, setDeviceList] = useState<any>([]);
  const [page, setPage] = useState(1);
  const [isSelectAllMultipleDevice, setIsSelectAllMultipleDevice] =
    useState(false);

  const advancedFilterRef = useRef<any>(null);
  const selectedDeviceRef = useRef(selectedDevice);

  useEffect(() => {
    selectedDeviceRef.current = selectedDevice;
  }, [selectedDevice]);

  const urlParamImei = new URLSearchParams(location.search).get('imei');
  const distributorParamId = new URLSearchParams(location.search).get(
    'owner_id',
  );

  // Create stable query parameters using useMemo
  const deviceQueryParams = useMemo(
    () => ({
      page: page,
      per_page: 20,
      search_keyword: searchText,
      statuses: filterStatus.join(','),
      is_activated: advancedFilters.inventory?.toString(),
      owner_id: selectedAccount?.id?.toString() || '',
      service_package_id: advancedFilters.servicePackage.join(','),
      device_category: advancedFilters.typeOfDevice.join(','),
      activated_at_from: getDateString(advancedFilters.activeDateRange.start),
      activated_at_to: getDateString(advancedFilters.activeDateRange.end),
      expired_at_from: getDateString(advancedFilters.expiredDateRange.start),
      expired_at_to: getDateString(advancedFilters.expiredDateRange.end),
    }),
    [page, searchText, filterStatus, selectedAccount?.id, advancedFilters],
  );

  const { data, isLoading, refetch } = useDeviceData(deviceQueryParams);

  // Create stable counter query parameters
  const counterQueryParams = useMemo(
    () => ({
      search_keyword: searchText,
      statuses: filterStatus.join(','),
      is_activated: advancedFilters.inventory?.toString(),
      owner_id: selectedAccount?.id?.toString() || '',
      service_package_id: advancedFilters.servicePackage.join(','),
      device_category: advancedFilters.typeOfDevice.join(','),
      activated_at_from: getDateString(advancedFilters.activeDateRange.start),
      activated_at_to: getDateString(advancedFilters.activeDateRange.end),
      expired_at_from: getDateString(advancedFilters.expiredDateRange.start),
      expired_at_to: getDateString(advancedFilters.expiredDateRange.end),
    }),
    [searchText, filterStatus, advancedFilters, selectedAccount?.id],
  );

  const { data: counterStatus, refetch: refetchCounterStatus } =
    useDeviceCountByStatus(counterQueryParams);

  const handleDeviceSelect = useCallback(
    (device: any) => {
      onDeviceSelect(device);
    },
    [onDeviceSelect],
  );

  // Use debouncedSearch only for manual actions like search input
  const debouncedSearch = useCallback(
    useDebounce(() => {
      setPage(1);
      // Manual refetch for user-initiated actions
      if (selectedAccount?.id) {
        refetch();
        refetchCounterStatus();
      }
    }, 300),
    [selectedAccount?.id, refetch, refetchCounterStatus],
  );

  const handleResetFilter = () => {
    setSearchText('');
    advancedFilterRef.current?.reset();
    setAdvancedFilters({
      ...DEFAULT_FILTER_STATE,
      lastedUpdated: Date.now(),
    });
  };

  const handleFetchMore = () => {
    if (data?.pagination?.total_pages > page) {
      setPage(page + 1);
    }
  };

  const onToggleMultipleSelect = (isActive: boolean) => {
    updateState({
      multipleSelect: { isActive, multipleDevice: [] },
      selectedDevice: null,
    });
  };

  const onOpenMoveModal = () => {
    openModal(MODAL_TYPES.MOVE_DEVICE);
  };

  const handleToggleSelectAllMultipleDevice = () => {
    if (isSelectAllMultipleDevice) {
      updateState({
        multipleSelect: { ...multipleSelect, multipleDevice: [] },
      });
    } else {
      updateState({
        multipleSelect: { ...multipleSelect, multipleDevice: deviceList },
      });
    }
    setIsSelectAllMultipleDevice(!isSelectAllMultipleDevice);
  };

  // Only trigger manual search when user types in search box
  useEffect(() => {
    if (searchText && selectedAccount?.id) {
      debouncedSearch();
    }
  }, [searchText, selectedAccount?.id, debouncedSearch]);

  useEffect(() => {
    if (profile?.id && !selectedAccount && !distributorParamId) {
      updateState({
        selectedAccount: {
          ...profile,
          level: 0,
        },
      });
      return;
    }
  }, [profile, distributorParamId]);

  useEffect(() => {
    if (page === 1) {
      const newData = data?.data || [];
      setDeviceList(newData);
      onUpdateDeviceList?.(newData);
    } else {
      const newData = [...deviceList, ...(data?.data || [])];
      setDeviceList(newData);
      onUpdateDeviceList?.(newData);
    }
  }, [data, page, deviceList]);

  // Auto select first device when deviceList changes
  const autoSelectTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    // Clear previous timeout
    if (autoSelectTimeoutRef.current) {
      clearTimeout(autoSelectTimeoutRef.current);
    }

    // Handle empty device list
    if (!deviceList.length) {
      if (selectedDeviceRef.current) {
        onDeviceSelect(null);
      }
      return;
    }

    if (ignoreFirstSelect) return;

    // Delay auto-select to avoid conflicts with rapid changes
    autoSelectTimeoutRef.current = setTimeout(() => {
      if (urlParamImei) {
        const device = deviceList.find(item => item.imei === urlParamImei);
        if (device && device.imei !== selectedDeviceRef.current?.imei) {
          onDeviceSelect(device);
        }
      } else {
        const firstDevice = deviceList[0];
        if (
          firstDevice &&
          firstDevice.imei !== selectedDeviceRef.current?.imei
        ) {
          onDeviceSelect(firstDevice);
        }
      }
    }, 100);

    return () => {
      if (autoSelectTimeoutRef.current) {
        clearTimeout(autoSelectTimeoutRef.current);
      }
    };
  }, [deviceList, urlParamImei, ignoreFirstSelect]);

  const multipleImeiSelected = multipleSelect.multipleDevice.map(
    item => item.imei,
  );

  return (
    <div
      className={twMerge(
        'flex flex-col bg-white-1000 border-grey-100 h-full   gap-3 p-4',
        className,
        isMap ? 'w-[360px]' : 'w-[400px]',
      )}
    >
      <div className='flex w-full flex-col items-center gap-3'>
        <div className='flex w-full items-center justify-between'>
          <div
            className={`flex items-center gap-2 ${BodyMdExtend} ${FontBold}`}
          >
            {!enableSelectAccount
              ? t('deviceList.deviceList')
              : selectedAccount?.username ||
                selectedAccount?.name ||
                selectedAccount?.full_name}

            <span
              className={`${BodySm} ${FontMedium} rounded-lg bg-black-1000 px-2 py-0.5 text-white-1000`}
            >
              {selectedAccount?.device_count || 0}
            </span>
            {enableAddDevice && (
              <LinkButton
                onClick={() => openModal(MODAL_TYPES.ADD_DEVICE)}
                size='small'
                className='h-6'
              >
                Nhập thiết bị
              </LinkButton>
            )}
          </div>
          {enableSelectAccount && (
            <SelectedAccountPopover
              profile={profile}
              accountBreadcrumb={accountBreadcrumb}
              distributorParamId={distributorParamId}
              selectedAccount={selectedAccount}
              onChange={onSelectAccount}
              onUpdateAccountBreadcrumb={onChangeBreadcrumb}
            />
          )}
          {enableViewAllDevice && (
            <LinkButton
              onClick={() => navigate('/map')}
              size='small'
              className='h-6 text-end'
            >
              {t('deviceList.viewAll')}
            </LinkButton>
          )}
        </div>
        <div className='flex w-full flex-1 items-center'>
          <RRInput
            type='text'
            value={searchText}
            prefixIcon={images.Icon.SearchLoupe}
            containerClassName='w-full flex-1'
            placeholder={t('deviceList.searchPlaceholder')}
            showClearButton={true}
            onChange={e => setSearchText(e.target.value)}
            onClear={() => setSearchText('')}
          />
          {advancedFilters?.isActive && (
            <Button
              className='ml-3 box-border flex h-10 w-24 flex-row items-center justify-center gap-1 rounded-lg border-[1.5px] border-solid border-grey-100 px-5 font-medium leading-[24px] text-black-1000 hover:border-grey-400 hover:text-grey-600'
              onClick={handleResetFilter}
            >
              <Icon src={images.Icon.CancelFilter} />
              Hủy lọc
            </Button>
          )}
        </div>
        <AllDeviceSelect
          counterStatus={counterStatus}
          status={filterStatus}
          onChange={setFilterStatus}
        />
        {enableAdvanceFilter && (
          <AdvancedFilter
            ref={advancedFilterRef}
            onFilterSelect={setAdvancedFilters}
          />
        )}
      </div>
      {enableMultipleSelect && (
        <>
          {multipleSelect?.isActive ? (
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Checkbox
                  checked={isSelectAllMultipleDevice}
                  onClick={handleToggleSelectAllMultipleDevice}
                  className=' text-grey-400'
                >
                  {t('deviceList.selectedAll')}
                </Checkbox>
                <LinkButton
                  onClick={onOpenMoveModal}
                  size='small'
                  className='h-6'
                  disabled={!multipleSelect.multipleDevice.length}
                >
                  Chuyển thiết bị
                </LinkButton>
              </div>

              <div
                className='flex items-center gap-3'
                onClick={() => onToggleMultipleSelect(false)}
              >
                <div className='text-sm text-grey-400'>
                  {`Đã chọn(${multipleSelect.multipleDevice.length})`}
                </div>
                <RemoveButton>
                  <Icon src={images.Icon.RadiusRemove} />
                </RemoveButton>
              </div>
            </div>
          ) : (
            <div className='flex items-center justify-end'>
              <LinkButton
                onClick={() => onToggleMultipleSelect(true)}
                size='small'
                className='h-6'
                disabled={deviceList.length === 0}
              >
                Chọn nhiều thiết bị
              </LinkButton>
            </div>
          )}
        </>
      )}
      {deviceList?.length === 0 && (
        <div className='flex h-[300px] items-center justify-center'>
          <EmptyPanel />
        </div>
      )}
      {deviceList?.length > 0 && (
        <div className='size-full flex-1'>
          <DeviceList
            isMap={isMap}
            loading={isLoading}
            data={deviceList}
            multipleImeiSelected={multipleImeiSelected}
            selectedDevice={selectedDevice}
            onSelectedDevice={handleDeviceSelect}
            fetchMore={handleFetchMore}
          />
        </div>
      )}
    </div>
  );
};

export default memo(DeviceListPanel);

const RemoveButton = tw.button`flex size-5 items-center justify-center rounded-[1000px] bg-red-10 p-[2px] text-sm`;
