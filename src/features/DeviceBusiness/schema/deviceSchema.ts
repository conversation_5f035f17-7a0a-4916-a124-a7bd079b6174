import * as yup from 'yup';

// Regex patterns
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const PHONE_REGEX = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;

// Base validation schemas
export const baseValidations = {
  username: yup.string().required('Tài khoản là bắt buộc'),

  password: yup
    .string()
    .required('Mật khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),

  passwordConfirmation: yup
    .string()
    .required('Xác nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),

  roleId: yup
    .number()
    .required('Vai trò là bắt buộc')
    .typeError('Vui lòng chọn vai trò'),

  fullName: yup
    .string()
    .required('Họ và tên là bắt buộc')
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự')
    .max(100, 'Họ và tên không được quá 100 ký tự'),

  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(
      PHONE_REGEX,
      'Số điện thoại không đúng định dạng (VD: 0912345678)',
    ),

  email: yup
    .string()
    .required('Email là bắt buộc')
    .matches(EMAIL_REGEX, 'Email không đúng định dạng'),

  address: yup.string().required('Địa chỉ là bắt buộc'),

  activeArea: yup.string().required('Khu vực hoạt động là bắt buộc'),
};

export const createDeviceSchema = yup.object({
  activationDate: yup.string().required('Ngày nhập là bắt buộc'),
  type: yup.string().required('Loại thiết bị là bắt buộc'),
  imei: yup.string().required('IMEI là bắt buộc'),
});
