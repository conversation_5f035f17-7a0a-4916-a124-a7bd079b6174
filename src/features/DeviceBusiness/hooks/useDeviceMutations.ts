import { useMutation, useQueryClient } from '@tanstack/react-query';

import { createApiMutationFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import showToast from 'components/RRToastMessage/Toast';

import {
  CreateDeviceData,
  DeviceDetailForm,
  TransferDevicesData,
} from '../types';

export const useDeviceMutations = (ownerId?: string) => {
  const queryClient = useQueryClient();

  const invalidateDevices = () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.devices.all,
    });
    if (ownerId) {
      queryClient.invalidateQueries({
        queryKey: queryKeys.devices.countByStatus(ownerId),
      });
    }
  };

  const invalidateAccounts = () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.accounts.all,
    });
  };

  const createDevice = useMutation<any, Error, CreateDeviceData>({
    mutationFn: createApiMutationFn<any, CreateDeviceData>(
      (data: CreateDeviceData) => ({
        method: 'POST',
        route: '/devices/imports',
        data: {
          device_category: data.type,
          owner_id: data.owner_id,
          devices: data.devices,
        },
      }),
    ),
    onSuccess: res => {
      invalidateDevices();
      showToast('success', res?.message || 'Nhập thiết bị thành công');
    },
  });

  const updateDevice = useMutation<
    any,
    Error,
    { imei: string; deviceInfo: DeviceDetailForm }
  >({
    mutationFn: createApiMutationFn<
      any,
      { imei: string; deviceInfo: DeviceDetailForm }
    >(({ imei, deviceInfo }) => ({
      method: 'PUT',
      route: `/devices/${imei}/device-info`,
      data: deviceInfo,
    })),
    onSuccess: (res, { imei }) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.devices.detail(imei),
      });
      invalidateDevices();
      showToast('success', 'Cập nhật thiết bị thành công');
    },
  });

  const transferDevices = useMutation<any, Error, TransferDevicesData>({
    mutationFn: createApiMutationFn<any, TransferDevicesData>(
      (data: TransferDevicesData) => ({
        method: 'POST',
        route: '/devices/batch_transfer',
        data: {
          to_user_id: data.to_user_id,
          note: data.note || '',
          imeis: data.imeis,
        },
      }),
    ),
    onSuccess: (res, { imeis }) => {
      console.log({ res });
      invalidateDevices();
      invalidateAccounts();
      // Invalidate affected devices
      imeis.forEach(imei => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.devices.detail(imei),
        });
      });
      showToast('success', res?.message || 'Chuyển thiết bị thành công');
    },
  });

  return {
    createDevice,
    updateDevice,
    transferDevices,
  };
};
