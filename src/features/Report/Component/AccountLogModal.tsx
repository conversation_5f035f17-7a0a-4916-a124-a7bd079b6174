import { useState } from 'react';

import { Modal, Popover, Tooltip } from 'antd';
import Cookies from 'js-cookie';
import { useDispatch } from 'react-redux';
import tw from 'tailwind-styled-components';

import { PageInfo } from 'features/AccountBusiness/types';
import { logoutAll } from 'features/Authentication/pages/Login/slice';
import { t } from 'i18next';

import { BodyMdBase, Colors } from 'assets/styles';
import { RRConfirmationModal } from 'components';
import { LinkButton } from 'components/Button';
import RRTable from 'components/RRTable';

const columns = [
  {
    title: t('history.type'),
    dataIndex: 'user_agent',
    key: 'user_agent',
  },
  {
    title: t('history.ipAddress'),
    dataIndex: 'ip',
    key: 'ip',
    render: (text: string) => (
      <Popover
        content={
          <div>
            <Action>{t('history.block')}</Action>
            <Action>{t('history.logoutDevice')}</Action>
          </div>
        }
        trigger='click'
        placement='bottom'
      >
        <IpAdress>{text}</IpAdress>
      </Popover>
    ),
  },
  {
    title: t('history.address'),
    dataIndex: 'city',
    key: 'city',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
  {
    title: 'Thời gian đăng nhập',
    dataIndex: 'loggedInAt',
    key: 'loggedInAt',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
  {
    title: 'Thời gian đăng xuất',
    dataIndex: 'loggedoutAt',
    key: 'loggedoutAt',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
];

interface AccountLogModalProps {
  visible: boolean;
  onClose: () => void;
  logs: Record<string, unknown>[];
  pageInfo: PageInfo;
  onCallNextPage: (page: number, per_page: number) => void;
  onChangePerPage: (per_page: number) => void;
}

const AccountLogModal = ({
  visible,
  onClose,
  pageInfo,
  logs,
  onCallNextPage,
  onChangePerPage,
}: AccountLogModalProps) => {
  const [currentPage, setCurrentPage] = useState(pageInfo?.page ?? 1);
  const [pageSize, setPageSize] = useState(20);
  const [showConfirm, setShowConfirm] = useState(false);
  const dispatch = useDispatch();

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    onCallNextPage(page, pageSize);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
    onChangePerPage(size);
  };

  const logOutAll = () => {
    dispatch(logoutAll({ id: Cookies.get('user_id') ?? '0' }));
  };

  return (
    <>
      <Modal
        centered
        title={t('history.modalTitle')}
        open={visible}
        onOk={onClose}
        onCancel={onClose}
        width={960}
        footer={null}
        zIndex={1000}
      >
        <Container>
          <TableHeader>
            <GrayText className={`${BodyMdBase}`}>
              {t('history.deviceLogged')}
            </GrayText>
            <LinkButton
              onClick={() => {
                setShowConfirm(true);
              }}
              size='small'
              className='my-3 h-6 text-end'
            >
              {t('history.logoutAll')}
            </LinkButton>
          </TableHeader>
          <RRTable
            columns={columns}
            data={logs}
            total={pageInfo?.total_count}
            currentPage={1}
            pageSize={2}
            hidePagination
          />
          <TableHeader>
            <GrayText className={`${BodyMdBase}`}>
              {t('history.deviceLogOut')}
            </GrayText>
          </TableHeader>
          <RRTable
            columns={columns}
            data={logs}
            total={pageInfo?.total_count}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </Container>
      </Modal>
      <RRConfirmationModal
        title={t('history.logouttitle')}
        message={t('history.logoutContent')}
        onCancel={() => {
          setShowConfirm(false);
        }}
        onConfirm={logOutAll}
        visible={showConfirm}
      />
    </>
  );
};

const Container = tw.div`flex h-full flex-col`;
const TableHeader = tw.div`flex justify-between items-center my-3`;
const GrayText = tw.span`text-grey-400`;
const IpAdress = tw.span`text-blue-200 cursor-pointer underline`;
const Action = tw.div`w-[168px] h-[40px] cursor-pointer content-center hover:bg-grey-100`;

export default AccountLogModal;

// const data = [
//   {
//     type: 'Chrome - Windows',
//     ipAddress: '**************',
//     address: 'Hà Nội',
//     loggedInAt: '31/07/2024 9:41',
//     loggedoutAt: '31/07/2024 10:00',
//   },
//   {
//     key: 2,
//     type: 'Firefox - macOS',
//     ipAddress: '***********',
//     address: 'TP.HCM',
//     loggedInAt: '01/08/2024 14:23',
//     loggedoutAt: '01/08/2024 15:05',
//   },
//   {
//     key: 3,
//     type: 'Edge - Windows',
//     ipAddress: '*************',
//     address: 'Đà Nẵng',
//     loggedInAt: '02/08/2024 08:15',
//     loggedoutAt: '02/08/2024 09:00',
//   },
//   {
//     key: 4,
//     type: 'Safari - iOS',
//     ipAddress: '123.456.78.90',
//     address: 'Hải Phòng',
//     loggedInAt: '03/08/2024 16:00',
//     loggedoutAt: '03/08/2024 16:45',
//   },
//   {
//     key: 5,
//     type: 'Chrome - Android',
//     ipAddress: '************',
//     address: 'Cần Thơ',
//     loggedInAt: '04/08/2024 10:20',
//     loggedoutAt: '04/08/2024 10:50',
//   },
//   {
//     key: 6,
//     type: 'Firefox - Linux',
//     ipAddress: '*************',
//     address: 'Huế',
//     loggedInAt: '05/08/2024 09:30',
//     loggedoutAt: '05/08/2024 10:10',
//   },
//   {
//     key: 7,
//     type: 'Chrome - macOS',
//     ipAddress: '***********',
//     address: 'Vũng Tàu',
//     loggedInAt: '06/08/2024 12:00',
//     loggedoutAt: '06/08/2024 12:30',
//   },
//   {
//     key: 8,
//     type: 'Edge - iOS',
//     ipAddress: '111.222.333.444',
//     address: 'Nha Trang',
//     loggedInAt: '07/08/2024 11:20',
//     loggedoutAt: '07/08/2024 11:50',
//   },
//   {
//     key: 9,
//     type: 'Safari - macOS',
//     ipAddress: '*************',
//     address: 'Đồng Nai',
//     loggedInAt: '08/08/2024 14:45',
//     loggedoutAt: '08/08/2024 15:15',
//   },
//   {
//     key: 10,
//     type: 'Chrome - Windows',
//     ipAddress: '************',
//     address: 'Hà Nội',
//     loggedInAt: '09/08/2024 13:50',
//     loggedoutAt: '09/08/2024 14:20',
//   },
//   {
//     key: 11,
//     type: 'Firefox - Windows',
//     ipAddress: '************',
//     address: 'Hà Giang',
//     loggedInAt: '10/08/2024 17:30',
//     loggedoutAt: '10/08/2024 18:00',
//   },
//   {
//     key: 12,
//     type: 'Chrome - Android',
//     ipAddress: '************',
//     address: 'Nghệ An',
//     loggedInAt: '11/08/2024 09:00',
//     loggedoutAt: '11/08/2024 09:45',
//   },
//   {
//     key: 13,
//     type: 'Edge - Windows',
//     ipAddress: '************',
//     address: 'Đà Lạt',
//     loggedInAt: '12/08/2024 10:15',
//     loggedoutAt: '12/08/2024 10:45',
//   },
//   {
//     key: 14,
//     type: 'Firefox - macOS',
//     ipAddress: '************',
//     address: 'Cần Thơ',
//     loggedInAt: '13/08/2024 16:20',
//     loggedoutAt: '13/08/2024 17:00',
//   },
//   {
//     key: 15,
//     type: 'Chrome - Linux',
//     ipAddress: '*************',
//     address: 'Hải Phòng',
//     loggedInAt: '14/08/2024 08:30',
//     loggedoutAt: '14/08/2024 09:15',
//   },
// ];
