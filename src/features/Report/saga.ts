import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { qsStringifyUrl } from 'services/api/utils';

import {
  fetchLoginActivities,
  fetchLoginActivitiesFailure,
  fetchLoginActivitiesSuccess,
} from './slice';

function* fetchLoginActivitiesSaga(
  action: ReturnType<typeof fetchLoginActivities>,
): SagaIterator {
  const { userId, page, pageSize } = action.payload;

  try {
    const response = yield call(callApi, {
      method: 'get',
      route: qsStringifyUrl({
        url: `/users/user-sessions`,
        query: {
          page: String(page),
          per_page: String(pageSize),
          is_active: 'true',
        },
      }),
    });

    if (response.success) {
      const { login_activities, page, total_count, total_pages } =
        response.response.data;
      console.log('fetchLoginActivitiesSaga', response.response.data);
      yield put(
        fetchLoginActivitiesSuccess({
          activities: login_activities,
          pageInfo: {
            page: page,
            totalPages: total_pages,
            totalCount: total_count,
          },
        }),
      );
    } else {
      yield put(fetchLoginActivitiesFailure());
    }
  } catch (error) {
    yield put(fetchLoginActivitiesFailure());
  }
}

export function* watchHistorySaga(): SagaIterator {
  yield takeEvery(fetchLoginActivities.type, fetchLoginActivitiesSaga);
}
