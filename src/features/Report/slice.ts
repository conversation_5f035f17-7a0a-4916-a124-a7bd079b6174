import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface PageInfo {
  page: number;
  totalPages: number;
  totalCount: number;
}

interface State {
  loading: boolean;
  activityLogs: any[];
  pageInfoActivityLogs: PageInfo;
}

const initialState: State = {
  loading: false,
  activityLogs: [],
  pageInfoActivityLogs: {
    page: 0,
    totalCount: 0,
    totalPages: 0,
  },
};

const slice = createSlice({
  name: 'history',
  initialState: initialState,
  reducers: {
    fetchLoginActivities(
      state,
      _: PayloadAction<{ userId: string; page: number; pageSize: number }>,
    ) {
      state.loading = true;
    },
    fetchLoginActivitiesSuccess(
      state,
      action: PayloadAction<{
        activities: any[];
        pageInfo: PageInfo;
      }>,
    ) {
      state.loading = false;
      state.activityLogs = action.payload.activities;
      state.pageInfoActivityLogs = action.payload.pageInfo;
    },
    fetchLoginActivitiesFailure(state) {
      state.loading = false;
    },
  },
});

export const {
  fetchLoginActivities,
  fetchLoginActivitiesSuccess,
  fetchLoginActivitiesFailure,
} = slice.actions;

export default slice.reducer;
