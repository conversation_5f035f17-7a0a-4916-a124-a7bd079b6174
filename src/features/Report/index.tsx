import { useEffect, useState } from 'react';

import type { ColumnsType } from 'antd/es/table';
import { useProfile } from 'hooks/useProfile';
import { useDispatch, useSelector } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyLg,
  BodyMdExtend,
  FontBold,
  FontSemibold,
  H4,
  TitleMd,
} from 'assets/styles';
import { Icon, RRCalendar, RRSelect } from 'components';
import { LinkButton, SecondaryButton } from 'components/Button';
import RRTable from 'components/RRTable';

import { selectActivityLogs } from './selectors';
import { fetchLoginActivities } from './slice';

const columns: ColumnsType<Record<string, unknown>> = [
  {
    title: 'STT',
    dataIndex: 'stt',
    key: 'stt',
    width: 60,
    align: 'center',
    render: (_: any, __: any, index: number) => index + 1,
  },
  {
    title: 'Thiết bị',
    dataIndex: 'deviceName',
    key: 'deviceName',
    align: 'center',
  },
  {
    title: 'Thời gian bắt đầu',
    dataIndex: 'startTime',
    key: 'startTime',
    align: 'center',
  },
  {
    title: 'Thời gian kết thúc',
    dataIndex: 'endTime',
    key: 'endTime',
    align: 'center',
  },
  {
    title: 'Quãng đường (km)',
    dataIndex: 'distance',
    key: 'distance',
    align: 'center',
  },
  {
    title: 'Thời gian',
    children: [
      {
        title: 'Nổ máy',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'center',
      },
      {
        title: 'Dừng nổ máy',
        dataIndex: 'endTime',
        key: 'endTime',
        align: 'center',
      },
    ],
  },
  {
    title: 'Mất kết nối',
    children: [
      {
        title: 'Số lần',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'center',
      },
      {
        title: 'Số lần',
        dataIndex: 'startTime',
        key: 'startTime',
        align: 'center',
      },
    ],
  },
];
interface ReportProps {}

const Report = ({}: ReportProps) => {
  const dispatch = useDispatch();
  const { profile } = useProfile();
  const activityLogs = useSelector(selectActivityLogs);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(9);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  console.log(activityLogs);
  const onPageSizeChange = (_: number, size: number) => {
    setPageSize(size);
  };

  useEffect(() => {
    if (profile?.id) {
      dispatch(
        fetchLoginActivities({
          userId: profile.id.toString(),
          page: currentPage,
          pageSize,
        }),
      );
    }
  }, [currentPage, pageSize, profile]);

  return (
    <Container>
      <MainContainer>
        <Header>
          <OverviewSection>
            <GreetingText className={BodyLg}>
              Xin chào {profile?.full_name}
            </GreetingText>
            <OverviewText className={H4}>Báo cáo dữ liệu</OverviewText>
          </OverviewSection>
        </Header>
        <Container className='mt-6 grid grid-cols-[1fr_1fr_1fr_auto] gap-2.5'>
          <div>
            <div className={`${BodyMdExtend} ${FontSemibold}`}>Thiết bị</div>
            <FilterSelect
              options={[]}
              placeholder={'Loại thiết bị'}
              className='h-[44px]'
              mode='multiple'
              onChange={(value, _) => {}}
              value={''}
            />
          </div>
          <div>
            <div className={`${BodyMdExtend} ${FontSemibold}`}>Thời gian</div>
            <CalendarContainer>
              <RRCalendar
                placeholder={'Ngày kích hoạt'}
                classNameInput={`h-[44px]`}
                minDateTime={undefined}
                maxDateTime={new Date()}
                onSelect={(type, date) => {}}
              />
              <div></div>
            </CalendarContainer>
          </div>
          <div>
            <div className={`${BodyMdExtend} ${FontSemibold}`}>
              Loại báo cáo
            </div>
            <FilterSelect
              options={[]}
              placeholder={'Loại thiết bị'}
              className='h-[44px]'
              mode='multiple'
              onChange={(value, _) => {}}
              value={''}
            />
          </div>
          <SearchButton className='h-[44px] w-full self-end justify-self-end'>
            {t('deviceLog.search')}
          </SearchButton>
        </Container>
        <Flex className='mb-3 mt-6 justify-between'>
          <Div className={`${FontBold} ${TitleMd}`}>
            {t('deviceLog.allData')}
          </Div>
          <Flex className='gap-4'>
            <LinkButton
              size='small'
              iconPosition='left'
              className='h-6'
              icon={<Icon src={images.Icon.ArrowRotate} />}
            >
              {t('deviceLog.refreshData')}
            </LinkButton>
            <SecondaryButton
              size='small'
              iconPosition='left'
              icon={<Icon src={images.Icon.FileExcel} />}
            >
              {t('deviceLog.downloadExcel')}
            </SecondaryButton>
            <SecondaryButton
              size='small'
              iconPosition='left'
              icon={<Icon src={images.Icon.FilePdf} />}
            >
              {t('deviceLog.downloadPDF')}
            </SecondaryButton>
          </Flex>
        </Flex>
        <div className='flex-1 '>
          <RRTable
            columns={columns}
            data={[]}
            total={0}
            currentPage={1}
            pageSize={2}
            hidePagination
          />
        </div>
      </MainContainer>
    </Container>
  );
};

const SearchButton = tw.button`rounded-lg bg-brand-300 p-2 text-black-1000`;
const CalendarContainer = tw.div`pt-2`;
const Container = tw.div`flex flex-col h-full`;
const MainContainer = tw.div`flex flex-col p-6`;
const Header = tw.div`flex justify-between `;
const OverviewSection = tw.div`flex flex-col`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const Div = tw.div``;
const Button = tw.button`rounded-lg p-2 text-black-1000 border border-grey-100 items-center`;
const Flex = tw.div`flex`;
const ButtonHeader = tw(Button)`flex h-[32px] flex gap-2`;
const FilterSelect = tw(RRSelect)`h-[40px]`;

export default Report;
