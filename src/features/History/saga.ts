import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { qsStringifyUrl } from 'services/api/utils';

import {
  fetchCurrentSession,
  fetchCurrentSessionFailure,
  fetchCurrentSessionSuccess,
  fetchLoginActivities,
  fetchLoginActivitiesFailure,
  fetchLoginActivitiesSuccess,
} from './slice';

function* fetchLoginActivitiesSaga(
  action: ReturnType<typeof fetchLoginActivities>,
): SagaIterator {
  const { userId, page, pageSize } = action.payload;

  try {
    const response = yield call(callApi, {
      method: 'get',
      route: qsStringifyUrl({
        url: `/users/user-sessions`,
        query: {
          page: String(page),
          per_page: String(pageSize),
          is_active: 'true',
        },
      }),
    });

    if (response.success) {
      const { login_activities, page, total_count, total_pages } =
        response.response.data;
      console.log('fetchLoginActivitiesSaga', response.response.data);
      yield put(
        fetchLoginActivitiesSuccess({
          activities: login_activities,
          pageInfo: {
            page: page,
            totalPages: total_pages,
            totalCount: total_count,
          },
        }),
      );
    } else {
      yield put(fetchLoginActivitiesFailure());
    }
  } catch (error) {
    yield put(fetchLoginActivitiesFailure());
  }
}

function* fetchCurrentSessionSaga(
  action: ReturnType<typeof fetchCurrentSession>,
): SagaIterator {
  const { userId, page, pageSize } = action.payload;

  try {
    const response = yield call(callApi, {
      method: 'get',
      route: qsStringifyUrl({
        url: `/users/user-sessions/current-session`,
        query: {
          page: String(page),
          per_page: String(pageSize),
          is_active: 'true',
        },
      }),
    });

    if (response.success) {
      const currentSession = response.response.data;
      console.log('fetchCurrentSessionSaga', response.response.data);
      yield put(
        fetchCurrentSessionSuccess({
          currentSession: currentSession,
        }),
      );
    } else {
      yield put(fetchCurrentSessionFailure());
    }
  } catch (error) {
    yield put(fetchCurrentSessionFailure());
  }
}

export function* watchHistorySaga(): SagaIterator {
  yield takeEvery(fetchLoginActivities.type, fetchLoginActivitiesSaga);
  yield takeEvery(fetchCurrentSession.type, fetchCurrentSessionSaga);
}
