import { useEffect, useState } from 'react';

import { Popover, Tooltip } from 'antd';
import { useProfile } from 'hooks/useProfile';
import { useDispatch, useSelector } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, BodyMdBase, Colors, H4 } from 'assets/styles';
import { Icon } from 'components';
import RRTable from 'components/RRTable';

import {
  selectActivityLogs,
  selectActivityLogsLoading,
  selectCurrentSession,
  selectCurrentSessionLoading,
} from './selectors';
import { fetchCurrentSession, fetchLoginActivities } from './slice';

const columns = [
  {
    title: t('history.type'),
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: t('history.ipAddress'),
    dataIndex: 'ipAddress',
    key: 'ipAddress',
    render: (text: string) => (
      <Popover
        content={
          <div>
            <Action>{t('history.block')}</Action>
            <Action>{t('history.logoutDevice')}</Action>
          </div>
        }
        trigger='click'
        placement='bottom'
      >
        <IpAdress>{text}</IpAdress>
      </Popover>
    ),
  },
  {
    title: t('history.address'),
    dataIndex: 'address',
    key: 'address',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
  {
    title: 'Thời gian đăng nhập',
    dataIndex: 'loggedInAt',
    key: 'loggedInAt',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
  {
    title: 'Thời gian đăng xuất',
    dataIndex: 'loggedoutAt',
    key: 'loggedoutAt',
    render: (text: string) => (
      <Tooltip
        title={text}
        color={'white'}
        overlayInnerStyle={{
          backgroundColor: Colors.white[1000],
          color: Colors.black[1000],
        }}
      >
        <div>{text}</div>
      </Tooltip>
    ),
  },
];

interface HistoryProps {}

const History = ({}: HistoryProps) => {
  const dispatch = useDispatch();
  const { profile } = useProfile();
  const activityLogs = useSelector(selectActivityLogs);
  const activityLogsLoading = useSelector(selectActivityLogsLoading);
  const currentSession = useSelector(selectCurrentSession);
  const currentSessionLoading = useSelector(selectCurrentSessionLoading);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(9);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
  };

  useEffect(() => {
    if (profile?.id) {
      dispatch(
        fetchLoginActivities({
          userId: profile.id.toString(),
          page: currentPage,
          pageSize,
        }),
      );

      dispatch(
        fetchCurrentSession({
          userId: profile.id.toString(),
          page: 1,
          pageSize: 10,
        }),
      );
    }
  }, [currentPage, pageSize, profile]);

  return (
    <Container>
      <MainContainer>
        <LeftContainer>
          <Header>
            <OverviewSection>
              <GreetingText className={BodyLg}>
                Xin chào {profile?.full_name}
              </GreetingText>
              <OverviewText className={H4}>Lịch sử đăng nhập</OverviewText>
            </OverviewSection>
          </Header>
          <TableHeader>
            <GrayText className={`${BodyMdBase}`}>
              {t('history.deviceLogged')}
            </GrayText>
            <BlueText className={`${BodyMdBase}`}>
              {t('history.logoutAll')}
            </BlueText>
          </TableHeader>
          <RRTable
            columns={columns}
            data={[]}
            total={0}
            currentPage={1}
            pageSize={2}
            hidePagination
          />
          <TableHeader>
            <GrayText className={`${BodyMdBase}`}>
              {t('history.deviceLogOut')}
            </GrayText>
          </TableHeader>
          <RRTable
            columns={columns}
            data={[]}
            total={0}
            currentPage={currentPage}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </LeftContainer>
        <RightContainer className='gap-3'>
          <LeftHeader>{t('history.leftHeader')}</LeftHeader>
          <LeftContent>
            <LeftRowContainer>
              <DesktopIcon>
                <Icon src={images.Icon.Desktop} />
              </DesktopIcon>
              <div className='flex-1 flex-col'>
                <BrowserInfoLabel>Chrome - Windows</BrowserInfoLabel>
                <div className='flex items-center gap-2'>
                  <Icon src={images.Icon.ArrowLocation} />
                  <SubText>**************</SubText>
                </div>
              </div>
            </LeftRowContainer>
            <LeftRowContainer>
              <Icon src={images.Icon.PinLocation} />
              <ColumnGap2>
                <SubText>{t('history.address')}</SubText>
                <SubText className='text-black'>Hồ Chí Minh, Việt Nam</SubText>
              </ColumnGap2>
            </LeftRowContainer>
            <Divider />
            <LeftRowContainer>
              <Icon src={images.Icon.ClockWatch} />
              <ColumnGap2>
                <SubText>{t('history.loginTime')}</SubText>
                <SubText className='text-black'>31/07/2024 9:41</SubText>
              </ColumnGap2>
            </LeftRowContainer>
            <Divider />
            <LeftRowContainer>
              <Icon src={images.Icon.ClockWatch} />
              <ColumnGap2>
                <SubText>{t('history.lastLogin')}</SubText>
                <SubText className='text-black'>31/07/2024 8:15</SubText>
              </ColumnGap2>
            </LeftRowContainer>
          </LeftContent>
          <Subheadline>{t('history.blockManagementTitle')}</Subheadline>
          <LeftRowContainer>
            <ColumnGap2>
              <BrowserInfoLabel className='text-sm'>
                Chrome - Windows
              </BrowserInfoLabel>
              <FullRowGap>
                <Icon src={images.Icon.ArrowLocation} />
                <SubText>{t('history.IPAddress')}</SubText>
                <SubText className='text-black'>**************</SubText>
              </FullRowGap>
            </ColumnGap2>

            <ActionButton>{t('history.unlockButton')}</ActionButton>
          </LeftRowContainer>
          <Divider />
          <LeftRowContainer>
            <ColumnGap2>
              <BrowserInfoLabel className='text-sm'>
                Chrome - Windows
              </BrowserInfoLabel>
              <FullRowGap>
                <Icon src={images.Icon.ArrowLocation} />
                <SubText>{t('history.IPAddress')}</SubText>
                <SubText className='text-black'>**************</SubText>
              </FullRowGap>
            </ColumnGap2>
            <ActionButton>{t('history.unlockButton')}</ActionButton>
          </LeftRowContainer>
          <Divider />
        </RightContainer>
      </MainContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col`;
const RightContainer = tw.div`flex flex-col flex-1 p-6`;
const LeftContainer = tw.div`flex flex-2 flex-col w-[70%] border-r border-grey-100 p-6`;
const MainContainer = tw.div`flex flex-1`;
const Header = tw.div`flex justify-between `;
const OverviewSection = tw.div`flex flex-col`;
const LeftHeader = tw.span`text-lg font-bold`;
const LeftContent = tw.div`flex flex-col gap-3 rounded-2xl border-2 border-grey-100 p-4 mb-3`;
const LeftRowContainer = tw.div`flex flex-row gap-3`;
const BrowserInfoLabel = tw.div`text-black font-['Inter'] text-base font-bold leading-normal`;
const DesktopIcon = tw.div`rounded-lg border border-grey-100 p-3`;
const SubText = tw.div`font-['Inter'] text-sm font-normal leading-normal text-grey-600`;
const ColumnGap2 = tw.div`inline-flex flex-1 flex-col gap-2`;
const Divider = tw.div`flex h-px bg-grey-100`;
const Subheadline = tw.span`text-sm font-medium font-['Inter'] leading-normal text-grey-400`;
const FullRowGap = tw.div`inline-flex items-center justify-start gap-1 self-stretch`;
const ActionButton = tw.button`text-blue-200 font-['Inter'] text-sm font-medium leading-tight`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const TableHeader = tw.div`flex justify-between items-center my-3`;
const GrayText = tw.span`text-grey-400`;
const BlueText = tw.span`text-blue-200 cursor-pointer`;
const IpAdress = tw.span`text-blue-200 cursor-pointer underline`;
const Action = tw.div`w-[168px] h-[40px] cursor-pointer content-center`;

export default History;

const data = [
  {
    key: 1,
    type: 'Chrome - Windows',
    ipAddress: '**************',
    address: 'Hà Nội',
    loggedInAt: '31/07/2024 9:41',
    loggedoutAt: '31/07/2024 10:00',
  },
  {
    key: 2,
    type: 'Firefox - macOS',
    ipAddress: '***********',
    address: 'TP.HCM',
    loggedInAt: '01/08/2024 14:23',
    loggedoutAt: '01/08/2024 15:05',
  },
  {
    key: 3,
    type: 'Edge - Windows',
    ipAddress: '*************',
    address: 'Đà Nẵng',
    loggedInAt: '02/08/2024 08:15',
    loggedoutAt: '02/08/2024 09:00',
  },
  {
    key: 4,
    type: 'Safari - iOS',
    ipAddress: '123.456.78.90',
    address: 'Hải Phòng',
    loggedInAt: '03/08/2024 16:00',
    loggedoutAt: '03/08/2024 16:45',
  },
  {
    key: 5,
    type: 'Chrome - Android',
    ipAddress: '************',
    address: 'Cần Thơ',
    loggedInAt: '04/08/2024 10:20',
    loggedoutAt: '04/08/2024 10:50',
  },
  {
    key: 6,
    type: 'Firefox - Linux',
    ipAddress: '*************',
    address: 'Huế',
    loggedInAt: '05/08/2024 09:30',
    loggedoutAt: '05/08/2024 10:10',
  },
  {
    key: 7,
    type: 'Chrome - macOS',
    ipAddress: '***********',
    address: 'Vũng Tàu',
    loggedInAt: '06/08/2024 12:00',
    loggedoutAt: '06/08/2024 12:30',
  },
  {
    key: 8,
    type: 'Edge - iOS',
    ipAddress: '111.222.333.444',
    address: 'Nha Trang',
    loggedInAt: '07/08/2024 11:20',
    loggedoutAt: '07/08/2024 11:50',
  },
  {
    key: 9,
    type: 'Safari - macOS',
    ipAddress: '*************',
    address: 'Đồng Nai',
    loggedInAt: '08/08/2024 14:45',
    loggedoutAt: '08/08/2024 15:15',
  },
  {
    key: 10,
    type: 'Chrome - Windows',
    ipAddress: '************',
    address: 'Hà Nội',
    loggedInAt: '09/08/2024 13:50',
    loggedoutAt: '09/08/2024 14:20',
  },
  {
    key: 11,
    type: 'Firefox - Windows',
    ipAddress: '************',
    address: 'Hà Giang',
    loggedInAt: '10/08/2024 17:30',
    loggedoutAt: '10/08/2024 18:00',
  },
  {
    key: 12,
    type: 'Chrome - Android',
    ipAddress: '************',
    address: 'Nghệ An',
    loggedInAt: '11/08/2024 09:00',
    loggedoutAt: '11/08/2024 09:45',
  },
  {
    key: 13,
    type: 'Edge - Windows',
    ipAddress: '************',
    address: 'Đà Lạt',
    loggedInAt: '12/08/2024 10:15',
    loggedoutAt: '12/08/2024 10:45',
  },
  {
    key: 14,
    type: 'Firefox - macOS',
    ipAddress: '************',
    address: 'Cần Thơ',
    loggedInAt: '13/08/2024 16:20',
    loggedoutAt: '13/08/2024 17:00',
  },
  {
    key: 15,
    type: 'Chrome - Linux',
    ipAddress: '*************',
    address: 'Hải Phòng',
    loggedInAt: '14/08/2024 08:30',
    loggedoutAt: '14/08/2024 09:15',
  },
];
