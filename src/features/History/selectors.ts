import { RootState } from 'store/rootReducer';

export const selectActivityLogs = (state: RootState) =>
  state.history.activityLogs;

export const selectActivityLogsLoading = (state: RootState) =>
  state.history.loading;

export const selectCurrentSession = (state: RootState) =>
  state.history.currentSession;

export const selectCurrentSessionLoading = (state: RootState) =>
  state.history.currentSessionLoading;
