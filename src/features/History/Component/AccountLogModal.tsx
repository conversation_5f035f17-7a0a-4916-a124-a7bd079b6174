import { useEffect, useState } from 'react';

import { useMutation, useQuery } from '@tanstack/react-query';
import { Modal, Popover, Tooltip } from 'antd';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { qsStringifyUrl } from 'services/api/utils';
import { createApiQueryFn, queryClient } from 'services/reactQuery';
import { formatDateStr } from 'utils/date';

import images from 'assets/images';
import { BodyMdBase, Colors } from 'assets/styles';
import { Icon, RRConfirmationModal } from 'components';
import { LinkButton } from 'components/Button';
import RRTable from 'components/RRTable';

const renderTooltip = (text: string): JSX.Element => {
  return (
    <Tooltip
      title={text}
      color={'white'}
      overlayInnerStyle={{
        backgroundColor: Colors.white[1000],
        color: Colors.black[1000],
      }}
    >
      <div className='truncate-1-line'>{text}</div>
    </Tooltip>
  );
};

const columns = [
  {
    title: t('history.type'),
    dataIndex: 'user_agent',
    key: 'user_agent',
    render: renderTooltip,
  },
  {
    title: t('history.ipAddress'),
    dataIndex: 'ip',
    key: 'ip',
    render: (text: string) => (
      <Popover
        content={
          <div>
            <Action>{t('history.block')}</Action>
            <Action>{t('history.logoutDevice')}</Action>
          </div>
        }
        trigger='click'
        placement='bottom'
      >
        <IpAddress>{text}</IpAddress>
      </Popover>
    ),
  },
  {
    title: t('history.address'),
    dataIndex: 'address',
    key: 'address',
    render: renderTooltip,
  },
  {
    title: 'Thời gian đăng nhập',
    dataIndex: 'loggedInAt',
    key: 'loggedInAt',
    render: renderTooltip,
  },
  {
    title: 'Thời gian đăng xuất',
    dataIndex: 'loggedOutAt',
    key: 'loggedOutAt',
    render: renderTooltip,
  },
] as any;

const defaultData = {
  data: [],
  pagination: {
    page: 1,
    total_count: 0,
    total_pages: 1,
  },
};

interface AccountLogModalProps {
  accountId: string;
  visible: boolean;
  onClose: () => void;
}

const AccountLogModal = ({
  accountId,
  visible,
  onClose,
}: AccountLogModalProps) => {
  const [pageSize, setPageSize] = useState(20); // for logs Out
  const [currentPage, setCurrentPage] = useState(1); // for logs Out
  const [showConfirm, setShowConfirm] = useState(false);

  const { data: logsIn = [], isLoading: logsInLoading } = useQuery({
    queryKey: ['account-logs-in', accountId],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: `/users/${accountId}/user-sessions`,
        query: {
          is_active: 'true',
          page: '1',
          per_page: '20',
        },
      }),
    }),
    enabled: !!accountId && visible,
    select: (data: any) => {
      const formatData = (data.user_sessions || []).map(item => ({
        ...item,
        loggedInAt: formatDateStr(item.logged_in_at),
        address: `${item.browser}, ${item.platform}`,
        key: item.id,
      }));
      return formatData;
    },
  });

  const {
    data: { data: logsOut, pagination } = defaultData,
    isLoading: logsOutLoading,
  } = useQuery({
    queryKey: ['account-logs-out', accountId, currentPage, pageSize],
    queryFn: createApiQueryFn({
      method: 'GET',
      route: qsStringifyUrl({
        url: `/users/${accountId}/user-sessions`,
        query: {
          is_active: 'false',
          page: currentPage.toString(),
          per_page: pageSize.toString(),
        },
      }),
    }),
    enabled: !!accountId && visible,
    staleTime: 0,
    select: (data: any) => {
      const formatData = (data.user_sessions || []).map(item => ({
        ...item,
        loggedOutAt: formatDateStr(item.logged_out_at),
        address: `${item.browser}, ${item.platform}`,
        key: item.id,
      }));
      return {
        data: formatData,
        pagination: {
          page: data.page,
          total_count: data.total_count,
          total_pages: data.total_pages,
        },
      };
    },
  });

  // how to reset logsIn, logOut data when accountId changes or visible changes?

  const signOutMutation = useMutation({
    mutationFn: createApiQueryFn({
      method: 'POST',
      route: `/users/${accountId}/sign_out_all_sessions`,
    }),
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const onPageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  const logOutAll = () => {
    signOutMutation.mutate(undefined, {
      onSuccess: () => {
        setShowConfirm(false);
        onClose();
      },
    });
  };

  useEffect(() => {
    if (!visible && accountId) {
      queryClient.removeQueries({
        queryKey: ['account-logs-in', accountId],
      });
      queryClient.removeQueries({
        queryKey: ['account-logs-out', accountId],
      });
    }
  }, [visible, accountId, queryClient]);

  return (
    <>
      <Modal
        centered
        title={t('history.modalTitle')}
        open={visible}
        onOk={onClose}
        onCancel={onClose}
        width='80%'
        footer={null}
        zIndex={1000}
        closeIcon={
          <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
        }
      >
        <Container>
          <div className='flex h-full flex-col gap-3'>
            <div>
              <TableHeader>
                <GrayText className={`${BodyMdBase}`}>
                  {t('history.deviceLogged')}
                </GrayText>
                <LinkButton
                  onClick={() => {
                    setShowConfirm(true);
                  }}
                  size='small'
                  className='my-3 h-6 text-end'
                >
                  {t('history.logoutAll')}
                </LinkButton>
              </TableHeader>
              <RRTable
                hidePagination
                columns={columns}
                data={logsIn}
                pageSize={1}
                currentPage={1}
                total={pagination.total_count}
                height={200}
                loading={logsInLoading}
              />
            </div>
            <div>
              <TableHeader>
                <GrayText className={`${BodyMdBase}`}>
                  {t('history.deviceLogOut')}
                </GrayText>
              </TableHeader>
              <RRTable
                columns={columns}
                data={logsOut}
                total={pagination.total_count}
                currentPage={currentPage}
                pageSize={pageSize}
                height={200}
                onPageChange={handlePageChange}
                onPageSizeChange={onPageSizeChange}
                loading={logsOutLoading}
              />
            </div>
          </div>
        </Container>
      </Modal>
      <RRConfirmationModal
        title={t('history.logouttitle')}
        message={t('history.logoutContent')}
        onCancel={() => {
          setShowConfirm(false);
        }}
        onConfirm={logOutAll}
        visible={showConfirm}
        isSubmitting={signOutMutation.isPending}
      />
    </>
  );
};

const Container = tw.div`flex h-full flex-col max-h-[90vh]`;
const TableHeader = tw.div`flex justify-between items-center my-3`;
const GrayText = tw.span`text-grey-400`;
const IpAddress = tw.span`text-blue-200 cursor-pointer underline`;
const Action = tw.div`w-[168px] h-[40px] cursor-pointer content-center`;

export default AccountLogModal;
