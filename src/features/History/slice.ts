import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface PageInfo {
  page: number;
  totalPages: number;
  totalCount: number;
}

interface State {
  loading: boolean;
  activityLogs: any[];
  pageInfoActivityLogs: PageInfo;
  currentSessionLoading: boolean;
  currentSession: any | null;
}

const initialState: State = {
  loading: false,
  activityLogs: [],
  pageInfoActivityLogs: {
    page: 0,
    totalCount: 0,
    totalPages: 0,
  },
  currentSessionLoading: false,
  currentSession: null,
};

const slice = createSlice({
  name: 'history',
  initialState: initialState,
  reducers: {
    fetchLoginActivities(
      state,
      _: PayloadAction<{ userId: string; page: number; pageSize: number }>,
    ) {
      state.loading = true;
    },
    fetchLoginActivitiesSuccess(
      state,
      action: PayloadAction<{
        activities: any[];
        pageInfo: PageInfo;
      }>,
    ) {
      state.loading = false;
      state.activityLogs = action.payload.activities;
      state.pageInfoActivityLogs = action.payload.pageInfo;
    },
    fetchLoginActivitiesFailure(state) {
      state.loading = false;
    },
    fetchCurrentSession(
      state,
      _: PayloadAction<{ userId: string; page: number; pageSize: number }>,
    ) {
      state.currentSessionLoading = true;
    },
    fetchCurrentSessionSuccess(
      state,
      action: PayloadAction<{ currentSession: any }>,
    ) {
      state.currentSessionLoading = false;
      state.currentSession = action.payload.currentSession;
    },
    fetchCurrentSessionFailure(state) {
      state.currentSessionLoading = false;
    },
  },
});

export const {
  fetchLoginActivities,
  fetchLoginActivitiesSuccess,
  fetchLoginActivitiesFailure,
  fetchCurrentSession,
  fetchCurrentSessionSuccess,
  fetchCurrentSessionFailure,
} = slice.actions;

export default slice.reducer;
