import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import { DeviceItemActionMapType } from 'components/DeviceHistory/types';
import { CollapseItemData } from 'components/RRCollapseTable';

// UI State interface
export interface MapUIState {
  // Position & Location
  currentPos?: ILatLng;

  // Filter & Selection
  selectedFilterAgencyAccount: CollapseItemData | null;

  // Modal & Visibility States
  showDeviceDetailVisible: boolean;
  showAreaVisible: boolean;
  showDeviceHistory: boolean;
  isShowCommandBox: boolean;

  // Map Controls
  radius: number;

  // Current Action
  currentDeviceItemAction?: DeviceItemActionMapType;
}

// Action types for reducer
export enum MapUIActionType {
  SET_CURRENT_POS = 'SET_CURRENT_POS',
  SET_SELECTED_FILTER_ACCOUNT = 'SET_SELECTED_FILTER_ACCOUNT',
  TOGGLE_DEVICE_DETAIL = 'TOGGLE_DEVICE_DETAIL',
  TOGGLE_AREA_MODAL = 'TOGGLE_AREA_MODAL',
  TOGGLE_DEVICE_HISTORY = 'TOGGLE_DEVICE_HISTORY',
  TOGGLE_COMMAND_BOX = 'TOGGLE_COMMAND_BOX',
  SET_RADIUS = 'SET_RADIUS',
  SET_CURRENT_ACTION = 'SET_CURRENT_ACTION',
  HANDLE_DEVICE_ITEM_ACTION = 'HANDLE_DEVICE_ITEM_ACTION',
  RESET_TO_DEVICE_LIST = 'RESET_TO_DEVICE_LIST',
}

// Action interfaces
export interface SetCurrentPosAction {
  type: MapUIActionType.SET_CURRENT_POS;
  payload: ILatLng | undefined;
}

export interface SetSelectedFilterAccountAction {
  type: MapUIActionType.SET_SELECTED_FILTER_ACCOUNT;
  payload: CollapseItemData | null;
}

export interface ToggleDeviceDetailAction {
  type: MapUIActionType.TOGGLE_DEVICE_DETAIL;
}

export interface ToggleAreaModalAction {
  type: MapUIActionType.TOGGLE_AREA_MODAL;
  payload?: boolean;
}

export interface ToggleDeviceHistoryAction {
  type: MapUIActionType.TOGGLE_DEVICE_HISTORY;
}

export interface ToggleCommandBoxAction {
  type: MapUIActionType.TOGGLE_COMMAND_BOX;
  payload?: boolean;
}

export interface SetRadiusAction {
  type: MapUIActionType.SET_RADIUS;
  payload: number;
}

export interface SetCurrentActionAction {
  type: MapUIActionType.SET_CURRENT_ACTION;
  payload: DeviceItemActionMapType | undefined;
}

export interface HandleDeviceItemActionAction {
  type: MapUIActionType.HANDLE_DEVICE_ITEM_ACTION;
  payload: DeviceItemActionMapType;
}

export interface ResetToDeviceListAction {
  type: MapUIActionType.RESET_TO_DEVICE_LIST;
}

// Union type for all actions
export type MapUIAction =
  | SetCurrentPosAction
  | SetSelectedFilterAccountAction
  | ToggleDeviceDetailAction
  | ToggleAreaModalAction
  | ToggleDeviceHistoryAction
  | ToggleCommandBoxAction
  | SetRadiusAction
  | SetCurrentActionAction
  | HandleDeviceItemActionAction
  | ResetToDeviceListAction;
