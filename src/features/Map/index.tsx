import { useCallback, useEffect, useState } from 'react';

import { Dayjs } from 'dayjs';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import DeviceListPanel from 'features/DeviceBusiness/components/DeviceListPanel';
import { useDeviceDetail, useDeviceState } from 'features/DeviceBusiness/hooks';
import { useMapState } from 'features/Map/hooks/useMapState';
import { getRoutePointData } from 'features/Map/hooks/useMapUtils';
import { DataItem } from 'features/Map/types';

import DeviceHistory from 'components/DeviceHistory/DeviceHistory';
import { DrawerDeviceDetail } from 'components/DeviceModal';
import RRMap from 'components/RRMap';

import CommandBox from './components/CommandBox';
import RadiusModal from './components/RadiusModal';
import RemoveRadiusConfirmation from './components/RemoveRadiusConfirmation';
import { useDeviceListRealtime } from './hooks/useMapData';

const Map = () => {
  const { profile: selectedProfile, profile } = useProfile();
  const {
    state,
    openModal,
    updateState,
    onDeviceSelect,
    onSelectAccount,
    onChangeBreadcrumb,
  } = useDeviceState();
  const { selectedDevice, multipleSelect } = state;

  // Combined Map State Management (UI + Data)
  const {
    // State values
    currentPos,
    showDeviceDetailVisible,
    showAreaVisible,
    showDeviceHistory,
    isShowCommandBox,
    radius,
    currentDeviceItemAction,

    // Actions
    setSelectedFilterAccount,
    setShowAreaVisible,
    setShowCommandBox,
    setRadius,
    resetToDeviceList,
    handleItemActionClick,
    closeDeviceDetail,
    setCurrentPos,

    // Map handlers
    onCreateZone,
    onDeleteZone,
  } = useMapState({
    selectedDevice,
  });
  const [selectedPreviewDate, setSelectedPreviewDate] = useState<
    Dayjs | undefined
  >(undefined);
  const [currentViewData, setCurrentViewData] = useState<
    DataItem | undefined
  >();
  const [pathPoints, setPathPoints] = useState<any>([]);

  const [deviceList, setDeviceList] = useState<any>([]);
  const deviceImeiList = deviceList.map((item: any) => item.imei);
  const { data: devicesRealTime } = useDeviceListRealtime({
    imeiList: deviceImeiList,
    enabled: deviceImeiList.length > 0 && !showDeviceHistory,
  });

  // Hooks
  const { data: deviceDetail } = useDeviceDetail({
    imei: selectedDevice?.imei ?? '',
  });

  // Handlers

  const onCreateZoneHandler = useCallback(
    (value: number) => {
      if (
        currentPos &&
        typeof currentPos === 'object' &&
        'lat' in currentPos &&
        'lng' in currentPos
      ) {
        onCreateZone(value, [currentPos.lat, currentPos.lng]);
      }
    },
    [currentPos, onCreateZone],
  );

  const handleViewPreview = (previewDate?: Dayjs, item?: DataItem) => {
    setSelectedPreviewDate(prev =>
      prev && prev.isSame(previewDate) ? undefined : previewDate,
    );
    setCurrentViewData(item);

    const newPoints = getRoutePointData(item);
    setPathPoints(newPoints);
  };

  const handleGoBackPreview = () => {
    if (selectedPreviewDate) {
      setSelectedPreviewDate(undefined);
      setCurrentViewData(undefined);
      setPathPoints([]);
    } else {
      resetToDeviceList();
    }
  };

  const handleSelectDevice = device => {
    if (device?.imei === selectedDevice?.imei) {
      return;
    }
    onDeviceSelect(device);
  };

  useEffect(() => {
    setSelectedFilterAccount(
      selectedProfile
        ? {
            id: selectedProfile.id?.toString() ?? '',
            key: selectedProfile.id?.toString() ?? '',
            name: selectedProfile.username || '',
            username: selectedProfile.username || '',
            level: 0,
          }
        : null,
    );
  }, [selectedProfile, setSelectedFilterAccount]);

  useEffect(() => {
    if (devicesRealTime) {
      setDeviceList(devicesRealTime);
    }
  }, [devicesRealTime]);

  return (
    <>
      <Container>
        <RRMap
          selectedPreviewDate={selectedPreviewDate}
          deviceList={deviceList}
          selectedDevice={deviceDetail}
          path={pathPoints}
          currentViewData={currentViewData}
          radius={radius}
          currentDeviceItemAction={currentDeviceItemAction}
          onItemActionClick={handleItemActionClick}
        />

        <MainContainer>
          <DeviceListPanel
            isMap={true}
            className='w-[360px] rounded-xl p-4 shadow-panel'
            enableViewAllDevice={false}
            enableAdvanceFilter={false}
            enableAddDevice={false}
            enableSelectAccount={true}
            enableMultipleSelect={false}
            selectedAccount={state.selectedAccount || (profile as any)}
            multipleSelect={multipleSelect}
            profile={profile}
            selectedDevice={deviceDetail}
            onDeviceSelect={handleSelectDevice}
            openModal={openModal}
            updateState={updateState}
            onSelectAccount={onSelectAccount}
            onChangeBreadcrumb={onChangeBreadcrumb}
            onUpdateDeviceList={setDeviceList}
          />
        </MainContainer>

        {deviceDetail && (
          <div className='absolute inset-y-3 left-3 z-20 flex flex-col'>
            <DeviceHistory
              selectedPreviewDate={selectedPreviewDate}
              currentViewData={currentViewData}
              visible={showDeviceHistory}
              device={deviceDetail}
              onViewPreview={handleViewPreview}
              onGoBack={handleGoBackPreview}
            />
          </div>
        )}
        {radius > 0 && (
          <RemoveRadiusConfirmation
            radius={radius}
            onDelete={() => {
              onDeleteZone();
              setRadius(0);
            }}
          />
        )}
      </Container>
      {/* Modals */}

      {isShowCommandBox && deviceDetail && (
        <CommandBox
          visible={isShowCommandBox}
          onClose={() => setShowCommandBox(false)}
          device={deviceDetail}
        />
      )}

      <DrawerDeviceDetail
        visible={showDeviceDetailVisible}
        onClose={closeDeviceDetail}
        selectedImei={deviceDetail?.imei ?? ''}
      />

      <RadiusModal
        visible={showAreaVisible}
        onConfirm={value => {
          setRadius(value);
          setShowAreaVisible(false); // This will automatically reset currentAction
          onCreateZoneHandler(value);
        }}
        onCancel={() => setShowAreaVisible(false)} // This will automatically reset currentAction
      />
    </>
  );
};

export default Map;

const Container = tw.div`relative w-full h-full`;
const MainContainer = tw.div`absolute top-0 left-0 bottom-0 w-[360px] z-20 pointer-events-none flex flex-col p-3 bg-transparent & > * { pointer-events-auto }`;
