import { useMutation, useQueryClient } from '@tanstack/react-query';

import { queryKeys } from 'services/reactQuery/queryHelpers';

import { createMutation } from './queryUtils';

// Type definitions
interface SendCommandParams {
  imei: string;
  command: string;
}

interface CreateGeofenceParams {
  imei: string;
  name: string;
  radius: number;
  center_lat: number;
  center_lng: number;
}

interface DeleteGeofenceParams {
  imei: string;
}

export const useMapMutations = () => {
  const queryClient = useQueryClient();

  // Helper functions for cache invalidation
  const invalidateQueries = {
    commands: (imei: string) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.map.commands(imei) });
    },
    geofences: (imei: string) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.map.geofences(imei),
      });
    },
    deviceList: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.map.all });
    },
    all: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.map.all });
    },
  };

  // Mutations with proper typing
  const sendCommand = useMutation<any, Error, SendCommandParams>(
    createMutation<any, SendCommandParams>(
      ({ imei, command }) => ({
        method: 'POST',
        route: `/devices/${imei}/commands`,
        data: { command },
      }),
      {
        onSuccess: (_, { imei }) => invalidateQueries.commands(imei),
      },
    ),
  );

  const createGeofence = useMutation<any, Error, CreateGeofenceParams>(
    createMutation<any, CreateGeofenceParams>(
      ({ imei, name, radius, center_lat, center_lng }) => ({
        method: 'POST',
        route: `/devices/${imei}/geofences`,
        data: { name, radius, center_lat, center_lng },
      }),
      {
        onSuccess: (_, { imei }) => invalidateQueries.geofences(imei),
      },
    ),
  );

  const deleteGeofence = useMutation<any, Error, DeleteGeofenceParams>(
    createMutation<any, DeleteGeofenceParams>(
      ({ imei }) => ({
        method: 'DELETE',
        route: `/devices/${imei}/geofences`,
      }),
      {
        onSuccess: (_, { imei }) => invalidateQueries.geofences(imei),
      },
    ),
  );

  return {
    // Mutations
    sendCommand,
    createGeofence,
    deleteGeofence,

    // Cache utilities
    invalidateQueries,
  };
};
