import { useCallback, useReducer } from 'react';

import { ILatLng } from 'react-map4d-map/dist/models/LatLng';

import { DeviceItemActionMapType } from 'components/DeviceHistory/types';
import { CollapseItemData } from 'components/RRCollapseTable';

import { MapUIActionType } from '../types/ui-state.types';
import { initialMapUIState, mapUIReducer } from './mapUIReducer';
import { useMapMutations } from './useMapMutations';

interface UseMapStateParams {
  selectedDevice?: any;
}

export const useMapState = ({ selectedDevice }: UseMapStateParams) => {
  // UI State Management với useReducer
  const [state, dispatch] = useReducer(mapUIReducer, initialMapUIState);

  // Mutations
  const { createGeofence, deleteGeofence } = useMapMutations();

  // UI Action creators
  const setCurrentPos = useCallback((pos: ILatLng | undefined) => {
    dispatch({
      type: MapUIActionType.SET_CURRENT_POS,
      payload: pos,
    });
  }, []);

  const setSelectedFilterAccount = useCallback(
    (account: CollapseItemData | null) => {
      dispatch({
        type: MapUIActionType.SET_SELECTED_FILTER_ACCOUNT,
        payload: account,
      });
    },
    [],
  );

  const closeDeviceDetail = useCallback(() => {
    dispatch({
      type: MapUIActionType.TOGGLE_DEVICE_DETAIL,
      // Ensure it closes and resets currentAction
    });
  }, []);

  const setShowAreaVisible = useCallback((visible?: boolean) => {
    dispatch({
      type: MapUIActionType.TOGGLE_AREA_MODAL,
      payload: visible,
    });
  }, []);

  const setShowCommandBox = useCallback((visible?: boolean) => {
    dispatch({
      type: MapUIActionType.TOGGLE_COMMAND_BOX,
      payload: visible,
    });
  }, []);

  const setRadius = useCallback((radius: number) => {
    dispatch({
      type: MapUIActionType.SET_RADIUS,
      payload: radius,
    });
  }, []);

  const resetToDeviceList = useCallback(() => {
    dispatch({ type: MapUIActionType.RESET_TO_DEVICE_LIST });
  }, []);

  // Main handler for device item actions
  const handleItemActionClick = useCallback(
    (actionType: DeviceItemActionMapType) => {
      dispatch({
        type: MapUIActionType.HANDLE_DEVICE_ITEM_ACTION,
        payload: actionType,
      });
    },
    [],
  );

  // Map State handlers

  const onCreateZone = useCallback(
    (value: number, currentPos: [number, number]) => {
      if (!selectedDevice?.imei) return;
      createGeofence.mutate({
        imei: selectedDevice.imei,
        name: '',
        radius: value,
        center_lat: currentPos[0],
        center_lng: currentPos[1],
      });
    },
    [selectedDevice?.imei, createGeofence],
  );

  const onDeleteZone = useCallback(() => {
    if (!selectedDevice?.imei) return;
    deleteGeofence.mutate({ imei: selectedDevice.imei });
  }, [selectedDevice?.imei, deleteGeofence]);

  return {
    // UI State
    state,

    // Individual state properties (for easier access)
    currentPos: state.currentPos,
    showDeviceDetailVisible: state.showDeviceDetailVisible,
    showAreaVisible: state.showAreaVisible,
    showDeviceHistory: state.showDeviceHistory,
    isShowCommandBox: state.isShowCommandBox,
    radius: state.radius,
    currentDeviceItemAction: state.currentDeviceItemAction,

    // UI Actions
    setCurrentPos,
    setSelectedFilterAccount,
    closeDeviceDetail,
    setShowAreaVisible,
    setShowCommandBox,
    setRadius,
    resetToDeviceList,
    handleItemActionClick,

    // Map State Handlers
    onCreateZone,
    onDeleteZone,

    // Mutations
    createGeofence,
    deleteGeofence,
  };
};

export type UseMapStateReturn = ReturnType<typeof useMapState>;
