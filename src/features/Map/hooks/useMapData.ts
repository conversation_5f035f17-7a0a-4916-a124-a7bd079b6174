import { useQuery } from '@tanstack/react-query';

import { queryKeys } from 'services/reactQuery/queryHelpers';

import {
  STALE_TIMES,
  buildDateRangeParams,
  createDeviceQuery,
  createImeiQuery,
} from './queryUtils';

export interface GeocodeResponse {
  geocode_result?: {
    full_address?: string;
    formatted_address?: string;
    place_id?: string;
    types?: string[];
  };
}

export const useQueryCommands = (imei: string) => {
  return useQuery({
    queryKey: queryKeys.map.commands(imei),
    ...createImeiQuery(
      imei,
      '/devices/:imei/commands',
      {},
      STALE_TIMES.FREQUENT,
    ),
  });
};

export const useQueryCommandSamples = (imei: string) => {
  return useQuery({
    queryKey: queryKeys.map.commandSamples(imei),
    ...createImeiQuery(
      imei,
      '/devices/:imei/command_samples',
      {},
      STALE_TIMES.STATIC,
    ),
  });
};

export const useHistoryData = ({ imei, startDate, endDate, enabled }) => {
  return useQuery({
    queryKey: queryKeys.map.historyData(imei, startDate, endDate),
    ...createImeiQuery(
      imei,
      '/devices/:imei/history',
      buildDateRangeParams(startDate, endDate),
      STALE_TIMES.MODERATE,
    ),
    enabled,
    select: (data: any) => {
      (data.data || []).forEach(element => {
        let stopCount = 0;
        element.routes.forEach(route => {
          if (route.type === 'stop') {
            stopCount++;
            route.stopCount = stopCount;
          }
        });
      });
      return {
        data: data.data,
      };
    },
  });
};

export const useOverviewData = ({ imei, startDate, endDate, enabled }) => {
  return useQuery({
    queryKey: queryKeys.map.overviewData(imei, startDate, endDate),
    ...createImeiQuery(
      imei,
      '/devices/:imei/overview-report',
      buildDateRangeParams(startDate, endDate),
      STALE_TIMES.MODERATE,
    ),
    enabled,
  });
};

export const useMapGeocodeReverse = ({ lat, lng } = { lat: '', lng: '' }) => {
  const { data: address } = useQuery({
    queryKey: queryKeys.map.geocodeReverse(lat, lng),
    ...createImeiQuery('', '/geocode/reverse', { lat, lng }, 0),
    enabled: !!(Number(lat) && Number(lng)),
    select: (data: any) => {
      return (
        data?.geocode_result?.full_address ||
        'RR16-RR17 Hồng Lĩnh, Phường 15, Quận 10, Thành phố Hồ Chí Minh'
      );
    },
  });

  if (!Number(lat) || !Number(lng)) {
    return 'Mất kết nối';
  }

  return address;
};

export const useDeviceListRealtime = (params: {
  imeiList: string[];
  enabled: boolean;
}) => {
  return useQuery({
    queryKey: queryKeys.map.deviceListRealtime(params),
    ...createDeviceQuery(
      '/devices/tracking-infos',
      { imeis: params.imeiList.join(',') },
      STALE_TIMES.REALTIME,
    ),
    enabled: params.enabled,
    refetchInterval: STALE_TIMES.REALTIME,
    select: (data: any) => {
      return data?.devices || [];
    },
  });
};
