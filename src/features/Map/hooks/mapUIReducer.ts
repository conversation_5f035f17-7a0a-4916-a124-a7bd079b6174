import { DeviceItemActionMapType } from 'components/DeviceHistory/types';

import {
  type MapUIAction,
  MapUIActionType,
  type MapUIState,
} from '../types/ui-state.types';

// Initial state
export const initialMapUIState: MapUIState = {
  currentPos: undefined,
  selectedFilterAgencyAccount: null,
  showDeviceDetailVisible: false,
  showAreaVisible: false,
  showDeviceHistory: false,
  isShowCommandBox: false,
  radius: 0,
  currentDeviceItemAction: undefined,
};

// Reducer function
export const mapUIReducer = (
  state: MapUIState,
  action: MapUIAction,
): MapUIState => {
  switch (action.type) {
    case MapUIActionType.SET_CURRENT_POS:
      return {
        ...state,
        currentPos: action.payload,
      };

    case MapUIActionType.SET_SELECTED_FILTER_ACCOUNT:
      return {
        ...state,
        selectedFilterAgencyAccount: action.payload,
      };

    case MapUIActionType.TOGGLE_DEVICE_DETAIL:
      return {
        ...state,
        showDeviceDetailVisible: !state.showDeviceDetailVisible,
        // Reset currentAction khi close modal
        currentDeviceItemAction: !state.showDeviceDetailVisible
          ? state.currentDeviceItemAction
          : undefined,
      };

    case MapUIActionType.TOGGLE_AREA_MODAL:
      return {
        ...state,
        showAreaVisible: action.payload ?? !state.showAreaVisible,
        // Reset currentAction khi close modal
        currentDeviceItemAction:
          action.payload ?? !state.showAreaVisible
            ? state.currentDeviceItemAction
            : undefined,
      };

    case MapUIActionType.TOGGLE_DEVICE_HISTORY:
      return {
        ...state,
        showDeviceHistory: !state.showDeviceHistory,
      };

    case MapUIActionType.TOGGLE_COMMAND_BOX:
      return {
        ...state,
        isShowCommandBox: action.payload ?? !state.isShowCommandBox,
        // Reset currentAction khi close modal
        currentDeviceItemAction:
          action.payload ?? !state.isShowCommandBox
            ? state.currentDeviceItemAction
            : undefined,
      };

    case MapUIActionType.SET_RADIUS:
      return {
        ...state,
        radius: action.payload,
      };

    case MapUIActionType.SET_CURRENT_ACTION:
      return {
        ...state,
        currentDeviceItemAction: action.payload,
      };

    case MapUIActionType.HANDLE_DEVICE_ITEM_ACTION:
      return handleDeviceItemAction(state, action.payload);

    case MapUIActionType.RESET_TO_DEVICE_LIST:
      return {
        ...state,
        showDeviceHistory: false,
        currentDeviceItemAction: undefined,
      };

    default:
      return state;
  }
};

// Helper function to handle device item actions
const handleDeviceItemAction = (
  state: MapUIState,
  actionType: DeviceItemActionMapType,
): MapUIState => {
  const isCurrentAction = state.currentDeviceItemAction === actionType;

  switch (actionType) {
    case DeviceItemActionMapType.history:
      return {
        ...state,
        showDeviceHistory: !state.showDeviceHistory,
        currentDeviceItemAction: isCurrentAction ? undefined : actionType,
      };

    case DeviceItemActionMapType.device: {
      const newShowDeviceDetail = !state.showDeviceDetailVisible;
      return {
        ...state,
        showDeviceDetailVisible: newShowDeviceDetail,
        currentDeviceItemAction: newShowDeviceDetail ? actionType : undefined,
      };
    }

    case DeviceItemActionMapType.area:
      return {
        ...state,
        showAreaVisible: true,
        currentDeviceItemAction: actionType,
      };

    case DeviceItemActionMapType.message:
      return {
        ...state,
        isShowCommandBox: true,
        currentDeviceItemAction: actionType,
      };

    default:
      return state;
  }
};
