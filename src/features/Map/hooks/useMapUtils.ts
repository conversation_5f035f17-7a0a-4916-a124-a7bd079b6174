import { useMemo } from 'react';

import { CommandMessage } from '../types';

export interface Message {
  text: string;
  time: string;
  type: 'left' | 'right';
  status?: string;
  success?: boolean;
}

export type MessagesByDateAndTime = {
  [date: string]: {
    [time: string]: Message[];
  };
};

// Reusable message transformation hook
export const useMessageTransformation = (commands: CommandMessage[] = []) => {
  return useMemo(() => {
    if (!commands.length) return {};

    return commands
      .slice()
      .reverse()
      .reduce((acc: MessagesByDateAndTime, cmd: CommandMessage) => {
        const [date, time] = cmd.created_at.split(' ');
        const [hours, minutes] = time.split(':');
        const formattedTime = `${hours}:${minutes}`;

        if (!acc[date]) acc[date] = {};
        if (!acc[date][formattedTime]) acc[date][formattedTime] = [];

        // Add command and response as separate messages
        acc[date][formattedTime].push(
          {
            text: cmd.command_text,
            time: formattedTime,
            type: 'right',
            status: cmd.status,
            success: cmd.success,
          },
          {
            text: cmd.response,
            time: formattedTime,
            type: 'left',
            status: cmd.status,
            success: cmd.success,
          },
        );

        return acc;
      }, {});
  }, [commands]);
};
// Hook for route point calculations
export const getRoutePointData = (historyData: any) => {
  const routes = historyData?.routes;
  if (!routes) return [];

  const routePointData = routes.flatMap((route: any) => {
    // get status from route, flat for all points
    return route.points.map(point => ({
      ...point,
      status: point.device_status,
      // status: route.type || point.device_status,
    }));
  });

  if (!routePointData.length) return [];

  // const uniquePoints = new Set<string>();

  return routePointData
    .map(point => {
      return {
        lat: point.latitude,
        lng: point.longitude,
        ...point,
      };
    })
    .filter(point => {
      // const key = `${point.lat},${point.lng}`;
      if (!point.lat || !point.lng) {
        return false;
      }
      // if (uniquePoints.has(key)) {
      //   return false;
      // }
      // uniquePoints.add(key);
      return true;
    });
};
