import React, { useState } from 'react';

import { Trans } from 'react-i18next';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyMdBase, FontSemibold } from 'assets/styles';
import { RRConfirmationModal } from 'components';

import './styles.css';

interface RemoveRadiusConfirmationProps {
  radius: number;
  onDelete: () => void;
}

const RemoveRadiusConfirmation: React.FC<RemoveRadiusConfirmationProps> = ({
  radius,
  onDelete,
}) => {
  const [showConfirm, setShowConfirm] = useState(false);

  return (
    <div className='absolute left-0 top-0 size-full'>
      <Container className='radius'>
        <Trans
          i18nKey='map.removeRadiusContent'
          values={{
            radius: radius,
          }}
          components={{
            strong: <Bold className={`${FontSemibold} ${BodyMdBase}`} />,
          }}
        />
        <Icon
          onClick={() => {
            setShowConfirm(true);
          }}
          src={images.Icon.RadiusRemove}
          alt='Remove'
        />
      </Container>
      <RRConfirmationModal
        title={t('map.deleteTitle')}
        message={t('map.deleteContent')}
        onCancel={() => {
          setShowConfirm(false);
        }}
        onConfirm={onDelete}
        visible={showConfirm}
      />
    </div>
  );
};

export default RemoveRadiusConfirmation;

const Container = tw.div`
  absolute
  flex
  gap-2
  items-center
  rounded-xl
  bg-white-1000
  p-2
  shadow-xl
  cursor-pointer
  left-1/2
  transform
  -translate-x-1/2
  z-30
  mt-3
`;
const Icon = tw.img`size-5 rounded-full bg-red-10 p-1`;
const Bold = tw.strong`mx-1`;
