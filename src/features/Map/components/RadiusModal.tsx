import React, { useState } from 'react';
import { Modal, Slider } from 'antd';
import tw from 'tailwind-styled-components';
import { Colors, FontBold, FontMedium } from 'assets/styles';
import { t } from 'i18next';

interface RadiusModalProps {
  visible: boolean;
  onConfirm: (value: number) => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

const RadiusModal: React.FC<RadiusModalProps> = ({
  visible,
  onConfirm,
  onCancel,
  confirmText = 'Xác nhận',
  cancelText = 'Huỷ bỏ',
}) => {
  const [sliderValue, setSliderValue] = useState(0);

  return (
    <Modal
      open={visible}
      centered
      closable={false}
      title={'Tạo vùng'}
      onCancel={onCancel}
      footer={
        <Footer>
          <CancelButton
            className={`${FontMedium}`}
            key="cancel"
            onClick={onCancel}
          >
            {cancelText}
          </CancelButton>
          <OkButton
            className={`${FontMedium}`}
            key="confirm"
            onClick={() => onConfirm(sliderValue)}
          >
            {confirmText}
          </OkButton>
        </Footer>
      }
    >
      <Body>
        <Radius className={`${FontBold}`}>{t('map.radius')}</Radius>
        <div className="flex w-full items-center">
          <div>{t('map.radiusMin')}</div>
          <Slider
            className="flex-1"
            styles={{
              track: {
                background: Colors.black[1000],
              },
            }}
            min={0}
            step={1}
            max={400}
            onChange={setSliderValue}
            value={sliderValue}
          />
          <MaxValue>{t('map.radiusMax')}</MaxValue>
        </div>
      </Body>
    </Modal>
  );
};

export default RadiusModal;

const Body = tw.div`py-5`;
const Footer = tw.div`flex gap-3`;
const Radius = tw.div`mb-3`;
const MaxValue = tw.div`bg-grey-50 py-2 px-3 rounded-lg text-grey-600`;
const OkButton = tw.button`w-1/2 rounded-lg bg-brand-300 py-2 text-black-1000`;
const CancelButton = tw.button`w-1/2 rounded-lg border border-grey-100 py-2 text-black-1000`;
