import { useState } from 'react';

import { Button, Flex, UploadFile } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useProfile } from 'hooks/useProfile';
import { xor } from 'lodash';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';
import { callApi } from 'services/api/api';

import images from 'assets/images';
import { BodyLg, BodyMdExtend, H4 } from 'assets/styles';
import { RRUpload } from 'components';

interface ReportIssueProps {}

const commonIssues = [
  { label: 'Mất tín hiệu GPS', value: 'gps' },
  { label: 'Độ chính xác không cao', value: 'accuracy' },
  { label: 'Hao pin nhanh', value: 'battery' },
  { label: 'Giao diện phức tạp', value: 'ui' },
  { label: 'Lỗi phần mềm', value: 'software' },
  { label: '<PERSON><PERSON><PERSON> kho<PERSON>n không thể truy cập', value: 'account' },
];

const ReportIssue = ({}: ReportIssueProps) => {
  const { profile } = useProfile();
  const [selectedIssues, setSelectedIssues] = useState<
    (typeof commonIssues)[0][]
  >([]);
  const [description, setDescription] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleChange = (newFileList: UploadFile[]) => {
    console.log('newFileList', newFileList);
    setFileList(newFileList);
  };

  const handleIssueSelect = value => {
    setSelectedIssues(prev => xor(prev, [value]));
  };

  const handleDescriptionChange = e => {
    setDescription(e.target.value);
  };

  const handleSubmit = async () => {
    const formData = new FormData();
    formData.append(
      'issue_type',
      selectedIssues.map(issue => issue.value).join(','),
    );
    formData.append('description', description);
    fileList.forEach(file => {
      if (file.originFileObj) {
        formData.append('attachments', file.originFileObj);
      }
    });

    try {
      const response = await callApi({
        method: 'post',
        route: '/issue_reports',
        data: formData,
        headersConfig: { 'Content-Type': 'multipart/form-data' },
      });

      if (response.success) {
        // Optionally, show a success message or reset the form
      } else {
        console.error('Failed to report issue:', response.error);
        // Optionally, show an error message
      }
    } catch (error) {
      console.error('Error reporting issue:', error);
      // Optionally, show an error message
    }
  };

  return (
    <Container>
      <Header>
        <OverviewSection>
          <GreetingText className={BodyLg}>
            Xin chào {profile?.full_name}
          </GreetingText>
          <OverviewText className={H4}>
            {t('reportIssue.headerText')}
          </OverviewText>
        </OverviewSection>
      </Header>
      <MainContainer>
        <LeftContainer>
          <FormInstruction>{t('reportIssue.instruction')}</FormInstruction>
          <Flex wrap>
            {commonIssues.map(issue => {
              const selected = selectedIssues.includes(issue);
              return (
                <TagIssue
                  onClick={() => handleIssueSelect(issue)}
                  key={issue.value}
                  className={`${
                    selected ? 'bg-brand-300' : ''
                  } ${BodyMdExtend} hover:bg-grey-100`}
                >
                  {issue.label}
                </TagIssue>
              );
            })}
          </Flex>

          <h3 className='mb-2 font-medium'>Khác</h3>
          <TextArea
            placeholder={t('reportIssue.describePlaceholder')}
            value={description}
            onChange={handleDescriptionChange}
            className='w-full bg-grey-50'
            rows={6}
          />
          <UploadSection>
            <UploadIntro>{t('reportIssue.uploadIntro')}</UploadIntro>
            <RRUpload fileList={fileList} onChange={handleChange} />
          </UploadSection>
          <SendIssueButton
            icon={<img src={images.Icon.SendMessage} />}
            onClick={handleSubmit}
          >
            {t('reportIssue.sendReportButton')}
          </SendIssueButton>
        </LeftContainer>
        <RightContainer>
          <div>
            <img src={images.ReportIssue.CrashReportSection} />
          </div>
        </RightContainer>
      </MainContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col p-6`;
const FormInstruction = tw.span`text-sm font-normal leading-[24px]  text-grey-600 mb-3 mt-6`;
const RightContainer = tw.div`flex w-[45%]`;
const LeftContainer = tw.div`flex flex-1 flex-col w-[55%]`;
const MainContainer = tw.div`flex flex-1 `;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const TagIssue = tw.button`mb-2 mr-2 flex items-center justify-center rounded-full border border-grey-100 px-3 py-1 text-center`;
const SendIssueButton = tw(
  Button,
)`my-3 bg-brand-300 py-2 text-sm font-medium leading-[24px] flex items-center justify-center hover:text-black-1000 hover:border-0`;
const UploadIntro = tw.div`text-sm font-normal leading-[24px] my-3 text-grey-600`;
const UploadSection = tw.div``;

export default ReportIssue;
