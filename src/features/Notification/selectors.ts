import { RootState } from 'store/rootReducer';

export const selectUnreadNotifications = (state: RootState) =>
  state.notification.unreadNotifications;

export const selectReadNotifications = (state: RootState) =>
  state.notification.readNotifications;

export const selectLoading = (state: RootState) => state.notification.loading;

export const selectCurrentPage = (state: RootState) => state.notification.page;

export const selectTotalPages = (state: RootState) =>
  state.notification.total_pages;

export const selectTotalUnread = (state: RootState) =>
  state.notification.total_unread;
