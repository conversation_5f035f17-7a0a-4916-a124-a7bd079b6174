import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';

import { transformNotifications } from './adapters';
import {
  deleteAllRead,
  fetchNotifications,
  fetchNotificationsSuccess,
  markAllAsRead,
  markAsRead,
} from './slice';

function* fetchNotificationsSaga(
  action: ReturnType<typeof fetchNotifications>,
): SagaIterator {
  const { page, isRead } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: `/notifications?page=${page}&type=${isRead ? 'read' : 'unread'}`,
  });

  if (response.success) {
    const {
      unreadDataList: unreadNotifications,
      readDataList: readNotifications,
    } = transformNotifications(response.response.data.notifications, isRead);
    const { total_pages, total_count } = response.response.data;

    yield put(
      fetchNotificationsSuccess({
        unreadNotifications,
        readNotifications,
        page,
        total_pages,
        total_count,
        isRead,
      }),
    );
  }
}

function* deleteAllReadSaga(
  action: ReturnType<typeof deleteAllRead>,
): SagaIterator {
  const { readIds } = action.payload;

  try {
    const response: ApiResponse = yield call(callApi, {
      method: 'delete',
      route: '/notifications/delete-all',
      data: { ids: readIds },
    });

    if (response.success) {
      console.log('Successfully deleted read notifications');
    } else {
      console.error('Failed to delete read notifications');
    }
  } catch (error) {
    console.error('Error deleting read notifications:', error);
  }
}
function* markAsReadSaga(action: ReturnType<typeof markAsRead>) {
  const { ids } = action.payload;

  try {
    const response: ApiResponse = yield call(callApi, {
      method: 'post',
      route: '/notifications/read',
      data: { ids },
    });

    if (response.success) {
      console.log('Successfully marked notifications as read');
    } else {
      console.error('Failed to mark notifications as read');
    }
  } catch (error) {
    console.error('Error marking notifications as read:', error);
  }
}

function* markAllAsReadSaga(
  action: ReturnType<typeof markAllAsRead>,
): SagaIterator {
  const { ids } = action.payload;

  try {
    const response: ApiResponse = yield call(callApi, {
      method: 'post',
      route: '/notifications/read',
      data: { ids },
    });

    if (response.success) {
      console.log('Successfully marked notifications as read');
    } else {
      console.error('Failed to mark notifications as read');
    }
  } catch (error) {
    console.error('Error marking notifications as read:', error);
  }
}
export function* watchNotificationSaga(): SagaIterator {
  yield takeEvery(fetchNotifications.type, fetchNotificationsSaga);
  yield takeEvery(deleteAllRead.type, deleteAllReadSaga);
  yield takeEvery(markAsRead.type, markAsReadSaga);
  yield takeEvery(markAllAsRead.type, markAllAsReadSaga);
}
