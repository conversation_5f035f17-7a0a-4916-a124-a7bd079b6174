/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useMemo, useState } from 'react';

import { Button, Empty, List } from 'antd';
import { useProfile } from 'hooks/useProfile';
import { useDispatch, useSelector } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, FontMedium, FontRegular, H4 } from 'assets/styles';
import { Icon, RRConfirmationModal } from 'components';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import EmailSetting from './EmailSetting';
import HeadlineBox from './HeadlineBox';
import NotificationOptionSetting from './NotificationOptionSetting';
import {
  // selectLoading,
  selectReadNotifications,
  // selectTotalPages,
  selectUnreadNotifications,
} from './selectors';
import {
  ReportData,
  deleteAllRead,
  fetchNotifications,
  markAllAsRead,
  markAsRead,
} from './slice';

interface NotificationDeviceProps {}

const NotificationDevice: React.FC<NotificationDeviceProps> = () => {
  const dispatch = useDispatch();
  const { profile } = useProfile();
  const unReadNotifications = useSelector(selectUnreadNotifications);
  const readNotifications = useSelector(selectReadNotifications);
  // const loading = useSelector(selectLoading);
  // const totalPages = useSelector(selectTotalPages);

  console.log(unReadNotifications);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedReport, setSelectedReport] = useState<ReportData | undefined>(
    undefined,
  );
  const [readIdx, setReadIdx] = useState<number[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [showNotificationOptions, setShowNotificationOptions] =
    useState<boolean>(false);
  const [showEmailSetting, setShowEmailSetting] = useState<boolean>(false);
  const [userProfile, setUserProfile] = useState<{ email: string }>({
    email: '<EMAIL>',
  });

  const defaultData = useMemo(() => {
    const unreadData = [
      {
        type: 'unread',
        title: 'Chưa đọc',
        leftText: 'Đọc tất cả',
        prefixIcon: images.Icon.MailClose,
      },
      ...unReadNotifications,
    ];
    const readData = [
      {
        type: 'read',
        title: 'Đã đọc',
        leftText: 'Xoá tất cả',
        prefixIcon: images.Icon.MailOpen,
      },
      ...readNotifications,
    ];
    return unreadData.concat(readData);
  }, [unReadNotifications, readNotifications]);

  const removedAllReport = useMemo(() => false, []);

  // const loadMoreNotifications = () => {
  //   if (!loading && currentPage < totalPages) {
  //     setCurrentPage(prevPage => prevPage + 1);
  //   }
  // };

  const markToReadAll = () => {
    const unreadIds = unReadNotifications.map(notification => notification.id);
    dispatch(markAllAsRead({ ids: unreadIds }));
  };

  const handleReportItemClick = useCallback((item: ReportData) => {
    setSelectedReport(item);
    setReadIdx(prev => {
      const newReadIdx = [...prev, +item.id];
      return newReadIdx;
    });
    dispatch(markAsRead({ ids: [item.id] }));
  }, []);

  const handleConfirm = () => {
    selectedReport;
    setIsModalVisible(false);
    dispatch(
      deleteAllRead({
        readIds: selectedReport?.id ? [selectedReport.id] : [],
        isRead: selectedReport?.type === 'read',
      }),
    );
    setSelectedReport(undefined);
  };

  const deleteReadAll = () => {
    const readIds = readNotifications.map(notification => notification.id);
    dispatch(deleteAllRead({ readIds, isRead: true }));
  };

  useEffect(() => {
    dispatch(fetchNotifications({ page: currentPage, isRead: false }));
    dispatch(fetchNotifications({ page: currentPage, isRead: true }));
  }, [dispatch, currentPage]);

  return (
    <Container>
      <MainContainer className='w-1/3'>
        <Header>
          <OverviewSection className='flex-1'>
            <GreetingText className={BodyLg}>
              Xin chào {profile?.full_name}
            </GreetingText>
            <div className='flex flex-1 flex-row justify-between'>
              <OverviewText className={H4}>{t('sidebar.noti')}</OverviewText>
              <img
                src={images.Icon.Setting}
                alt='Settings'
                onClick={() => setShowNotificationOptions(true)}
              />
            </div>
          </OverviewSection>
        </Header>
        <RRScrollView>
          <div className=''>
            {removedAllReport ? (
              <Empty
                style={{
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
                image={images.Icon.EmptyNoNotification}
                description={t('notification.noData')}
              />
            ) : (
              <List
                className='h-full'
                dataSource={defaultData}
                renderItem={(item, index) => {
                  if ((item as any)?.prefixIcon) {
                    const { title, prefixIcon, type } = item as any;
                    const sizeOfReport =
                      defaultData.filter(i => i.type == type).length - 1;
                    return (
                      <HeadlineBox
                        className='mt-6'
                        title={`${title ?? ''}(${sizeOfReport})`}
                        leftText={(item as any)?.leftText}
                        prefixIcon={prefixIcon}
                        onClick={() => {
                          item.type == 'unread' && markToReadAll();
                          item.type == 'read' && deleteReadAll();
                        }}
                      />
                    );
                  }

                  return (
                    <div
                      className={`relative box-border flex w-full flex-col items-start justify-start text-left text-sm font-normal text-black-1000 ${
                        'id' in item && readIdx.includes(Number(item.id))
                          ? 'opacity-50'
                          : ''
                      }`}
                      onClick={() => handleReportItemClick(item as ReportData)}
                    >
                      <div className='flex w-full flex-row items-center justify-center border-b border-solid border-grey-100 px-0 py-3'>
                        <div className='flex-1 cursor-pointer leading-[24px]'>
                          {(item as ReportData).content}
                        </div>
                      </div>
                    </div>
                  );
                }}
              />
            )}
          </div>
        </RRScrollView>
      </MainContainer>
      <RightContainer>
        {!selectedReport && (
          <div className='flex flex-col items-center justify-center gap-6'>
            <img
              className='size-40'
              src={images.Icon.WaitingMap}
              alt='Waiting'
            />
            <div className='font-medium leading-[24px]'>
              {t('notification.waitingMessage')}
            </div>
          </div>
        )}
        {selectedReport && (
          <div className='absolute top-6 mx-4 flex flex-row items-center gap-4 rounded-xl border border-grey-100 bg-white-1000 px-4 py-3'>
            <div
              className={`flex-1 text-black-1000 ${FontMedium} ${FontRegular}`}
            >
              {selectedReport.content}
            </div>
            <Button
              shape='circle'
              icon={<Icon src={images.Icon.Trash} />}
              onClick={() => setIsModalVisible(true)}
              className='border-none bg-red-10 shadow-none hover:bg-red-alpha20'
            />
          </div>
        )}
      </RightContainer>
      {showNotificationOptions && (
        <NotificationOptionSetting
          visible={showNotificationOptions}
          onClose={() => setShowNotificationOptions(false)}
        />
      )}

      <RRConfirmationModal
        title={t('notification.confirmDeleteTitle')}
        message={t('notification.confirmDeleteMessage')}
        onCancel={() => setIsModalVisible(false)}
        onConfirm={handleConfirm}
        visible={isModalVisible}
      />
      <EmailSetting
        visible={showEmailSetting}
        onClose={() => setShowEmailSetting(false)}
        onFinished={email => setUserProfile({ email })}
      />
    </Container>
  );
};

const Container = tw.div`flex h-full`;
const RightContainer = tw.div`flex size-full flex-1 flex-col items-center justify-center border-l border-solid border-grey-100 p-3 text-base font-medium text-grey-600`;
const MainContainer = tw.div`p-6`;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;

export default NotificationDevice;
