import React, { useState } from 'react';
import { Drawer } from 'antd';
import './Notification.css';
import tw from 'tailwind-styled-components';
import { Icon } from 'components';
import images from 'assets/images';
import { BodyMdExtend, FontBold, TitleMd } from 'assets/styles';
import { t } from 'i18next';
import RRScrollView from 'components/RRScrollView/RRScrollView';
import { xor } from 'lodash';

interface NotificationOptionSettingProps {
  visible: boolean;
  onClose: () => void;
}

const commandList = [
  'Kết nối thiết bị',
  'Mở khóa trái phép',
  'Khởi động lại thiết bị',
  'Mở nắp thiết bị',
  'Kết nối thiết bị ngoại vi',
  'Tín hiệu vệ tinh',
  '<PERSON> chuyển',
  'Label',
  'Vùng địa lý',
  'Khóa điện',
  'Phòng vệ',
  '<PERSON><PERSON> l<PERSON>, va chạm',
  'SOS',
  'Cảnh báo đóng/mở cửa',
];

const NotificationOptionSetting: React.FC<NotificationOptionSettingProps> = ({
  visible,
  onClose,
}) => {
  const [selectedKey, setSelectedKey] = useState<string[]>([]);

  return (
    <Container>
      <Drawer
        className={`notification-option no-transition py-6`}
        closable={false}
        open={visible}
      >
        <Content>
          <div className="flex flex-1 flex-col border-r border-grey-100 px-4 py-2">
            <Header>
              <div
                className={`${BodyMdExtend} ${FontBold} ${TitleMd} leading-7`}
              >
                {t('notification.optionSettingLabel')}
              </div>
              <Icon src={images.Icon.X} onClick={onClose} />
            </Header>
            <RRScrollView>
              {commandList.map(item => {
                const checked = selectedKey.includes(item);
                return (
                  <div
                    key={item}
                    className="flex flex-row items-center justify-start self-stretch border-b-[0.5px] border-solid border-grey-100 px-2 py-3"
                    onClick={() => {
                      setSelectedKey(pre => xor(pre, [item]));
                    }}
                  >
                    <div className="flex flex-1 flex-row items-center justify-start">
                      <div className="relative w-full font-medium leading-[24px]">
                        {item}
                      </div>
                      {checked && <Icon src={images.Icon.CheckMark} />}
                    </div>
                  </div>
                );
              })}
            </RRScrollView>
          </div>
        </Content>
      </Drawer>
    </Container>
  );
};

export default NotificationOptionSetting;

const Container = tw.div`flex h-screen `;
const Content = tw.div`flex h-full overflow-y-hidden`;
const Header = tw.div`flex justify-between px-2`;
