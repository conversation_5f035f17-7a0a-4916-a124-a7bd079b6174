import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface ReportData {
  id: string;
  type: 'read' | 'unread';
  content: string;
}

interface NotificationState {
  unreadNotifications: ReportData[];
  readNotifications: ReportData[];
  loading: boolean;
  page: number;
  total_pages: number;
  total_count: number;
  total_unread: number;
}

const initialState: NotificationState = {
  unreadNotifications: [],
  readNotifications: [],
  loading: false,
  page: 1,
  total_pages: 1,
  total_count: 0,
  total_unread: 0,
};

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    fetchNotifications(
      state,
      payload: PayloadAction<{ page: number; isRead: boolean }>,
    ) {
      state.loading = true;
    },
    fetchNotificationsSuccess(
      state,
      action: PayloadAction<{
        unreadNotifications: ReportData[];
        readNotifications: ReportData[];
        page: number;
        total_pages: number;
        total_count: number;
        isRead: boolean;
      }>,
    ) {
      state.unreadNotifications =
        action.payload.page === 1
          ? action.payload.unreadNotifications.length > 0
            ? action.payload.unreadNotifications
            : state.unreadNotifications
          : [
              ...state.unreadNotifications,
              ...action.payload.unreadNotifications,
            ];

      state.readNotifications =
        action.payload.page === 1
          ? action.payload.readNotifications.length > 0
            ? action.payload.readNotifications
            : state.readNotifications
          : [...state.readNotifications, ...action.payload.readNotifications];

      state.page = action.payload.page;
      state.total_pages = action.payload.total_pages;
      state.total_count = action.payload.total_count;
      state.loading = false;
      if (!action.payload.isRead) {
        state.total_unread = action.payload.total_count;
      }
    },
    fetchNotificationsFailure(state) {
      state.loading = false;
    },
    deleteAllRead(
      state,
      action: PayloadAction<{ readIds: string[]; isRead: boolean }>,
    ) {
      if (action.payload.isRead) {
        state.readNotifications = state.readNotifications.filter(
          notification => !action.payload.readIds.includes(notification.id),
        );
      } else {
        console.log('Deleting unread notifications');
        state.unreadNotifications = state.unreadNotifications.filter(
          notification => !action.payload.readIds.includes(notification.id),
        );
      }
    },
    markAllAsRead(state, _: PayloadAction<{ ids: string[] }>) {
      state.readNotifications = [
        ...state.readNotifications,
        ...state.unreadNotifications.map(
          notification =>
            ({
              ...notification,
              type: 'read',
            }) as ReportData,
        ),
      ];
      state.unreadNotifications = [];
    },
    markAsRead(_, __: PayloadAction<{ ids: string[] }>) {
      // No state updates are needed here since the API handles the changes
      // and the data will be correct upon reload.
    },
    newNotification(
      state,
      action: PayloadAction<{
        id: string;
        content: string;
      }>,
    ) {
      const newNotification: ReportData = {
        id: action.payload.id,
        type: 'unread',
        content: action.payload.content,
      };
      state.unreadNotifications.unshift(newNotification);
      state.total_unread += 1;
    },
  },
});

export const {
  fetchNotifications,
  fetchNotificationsSuccess,
  fetchNotificationsFailure,
  deleteAllRead,
  markAllAsRead,
  markAsRead,
  newNotification,
} = notificationSlice.actions;

export default notificationSlice.reducer;
