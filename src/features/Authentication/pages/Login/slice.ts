import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

import { Account } from 'features/Profile/types';

import { LoginForm } from './types';

interface LoginState {
  data: string | undefined;
  profile?: Account;
  loading: boolean;
  error: string | null;
}

const initialState: LoginState = {
  data: '',
  profile: undefined,
  loading: false,
  error: null,
};

const slice = createSlice({
  name: 'login',
  initialState: initialState,
  reducers: {
    login(state, _: PayloadAction<{ data: LoginForm; callback: () => void }>) {
      state.loading = true;
    },
    loginSuccess(
      state,
      action: PayloadAction<{ token: string; user: Account }>,
    ) {
      Cookies.set('token', action.payload.token);
      Cookies.set('user_id', action.payload.user.id?.toString() ?? '');
      state.loading = false;
      state.profile = action.payload.user;
      state.data = action.payload.token;
    },
    updateProfile(state, action: PayloadAction<Account>) {
      state.profile = action.payload;
    },
    logout: () => {},
    logoutAll(state, _: PayloadAction<{ id }>) {
      state.loading = true;
    },
    logoutSuccess(state) {
      state.loading = false;
      state.data = undefined;
    },
  },
});

export const {
  login,
  logoutAll,
  loginSuccess,
  logout,
  logoutSuccess,
  updateProfile,
} = slice.actions;

export default slice.reducer;
