import { useEffect, useState } from 'react';

import { Checkbox, CheckboxProps } from 'antd';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { login } from 'features/Authentication/pages/Login/slice';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import { RRFieldInput, RRFieldPassword } from 'components';
import { LinkButton } from 'components/Button';

import AppDownloadBanner from './components/AppDownloadBanner';
import ForgotPassModal from './components/ForgotPassModal';
import {
  REMMEBER_CHECK_KEY,
  REMMEBER_PASSWORD_KEY,
  REMMEBER_US_NAME_KEY,
} from './constants';
import { selectLoginData } from './selectors';
import { LoginForm } from './types';

interface LoginProps {}

const Login = ({}: LoginProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [rememberMe, setRememberMe] = useState(false);
  const [isForgot, setIsForgot] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const data = useSelector(selectLoginData);

  const defaultValues = {
    username: localStorage.getItem(REMMEBER_US_NAME_KEY) ?? '',
    password: localStorage.getItem(REMMEBER_PASSWORD_KEY) ?? '',
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>({ defaultValues });

  const handleRememberMeChange: CheckboxProps['onChange'] = e => {
    setRememberMe(e.target.checked);
  };

  const onSubmit = (data: LoginForm) => {
    const callback = () => {
      navigate('/profile?active=password');
    };
    dispatch(login({ data, callback }));
    if (rememberMe) {
      localStorage.setItem(REMMEBER_US_NAME_KEY, data.username);
      localStorage.setItem(REMMEBER_PASSWORD_KEY, data.password);
      localStorage.setItem(REMMEBER_CHECK_KEY, rememberMe.toString());
    } else {
      localStorage.removeItem(REMMEBER_US_NAME_KEY);
      localStorage.removeItem(REMMEBER_PASSWORD_KEY);
      localStorage.removeItem(REMMEBER_CHECK_KEY);
    }
  };

  useEffect(() => {
    const isRemember = !!localStorage.getItem(REMMEBER_CHECK_KEY);
    if (isRemember) {
      setRememberMe(true);
    }
  }, []);

  return (
    <Container>
      <WelComeSection>
        <WelCome src={images.Login.SignInWelcome} alt='Navio Login' />
        <AppDownloadBanner />
      </WelComeSection>
      <MainFormContainer>
        <Form onSubmit={handleSubmit(onSubmit)}>
          <LogoSection>
            <MainLogo src={images.Login.Logo} alt='Icon' />
          </LogoSection>
          <FieldInputContainer>
            <RRFieldInput
              control={control}
              id='username'
              label={t('login.username')}
              placeholder={t('login.usernamePlaceholder')}
              prefixIcon={images.Icon.User}
              errors={errors}
            />
          </FieldInputContainer>
          <FieldInputContainer className={'mb-2'}>
            <RRFieldPassword
              control={control}
              label={t('login.password')}
              id='password'
              placeholder={t('login.passwordPlaceholder')}
              prefixIcon={images.Icon.Lock}
              errors={errors}
            />
          </FieldInputContainer>
          <ActionContainer>
            <CheckBoxSection>
              <Checkbox checked={rememberMe} onChange={handleRememberMeChange}>
                {t('login.remember')}
              </Checkbox>
            </CheckBoxSection>
            <LinkButton
              onClick={() => {
                setIsForgot(true);
                // navigate(UnauthenticatedRouteMap.ForgotPassword);
              }}
              size='small'
              className='my-3 h-6 text-end'
            >
              {t('login.forgotPassword')}
            </LinkButton>
          </ActionContainer>
          <SubmitButton className={`${ButtonSm} ${FontMedium}`} type='submit'>
            {t('login.login')}
          </SubmitButton>
        </Form>
      </MainFormContainer>
      <ForgotPassModal visible={isForgot} onCancel={() => setIsForgot(false)} />
    </Container>
  );
};

export default Login;

const Container = tw.div`flex h-screen`;
const WelComeSection = tw.div`relative w-1/2 flex-wrap items-center justify-center`;
const WelCome = tw.img`h-screen max-w-full`;
const MainFormContainer = tw.div`flex w-1/2 items-center justify-center`;
const Form = tw.form`w-full max-w-[360px]`;
const LogoSection = tw.div`mb-10 flex flex-col items-center`;
const MainLogo = tw.img`mb-4`;
const FieldInputContainer = tw.div`relative mb-3 w-full`;
const ActionContainer = tw.div`mb-10 flex w-[360px] items-center justify-between`;
const CheckBoxSection = tw.label`flex items-center`;
const SubmitButton = tw.button`w-[360px] rounded-lg bg-brand-300 py-2 text-black-1000`;
