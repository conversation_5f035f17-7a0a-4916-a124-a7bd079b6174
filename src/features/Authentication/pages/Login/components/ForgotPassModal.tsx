import React, { useState } from 'react';

import { Modal } from 'antd';
import { useForm } from 'react-hook-form';
import { Trans } from 'react-i18next';
import { useDispatch } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { FontBold, FontMedium, TitleMd } from 'assets/styles';
import { RRFieldInput } from 'components';

import { forgotPassword } from '../../ForgotPassword/slice';

interface ForgotPassModalProps {
  visible: boolean;
  onCancel: () => void;
}

export interface ForgotPasswordForm {
  email: string;
}

const ForgotPassModal: React.FC<ForgotPassModalProps> = ({
  visible,
  onCancel,
}) => {
  const [isSubmited, setIsSubmited] = useState(false);
  const dispatch = useDispatch();
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ForgotPasswordForm>();

  const email = watch('email');
  const onSubmit = (data: ForgotPasswordForm) => {
    dispatch(forgotPassword({ data }));
    setIsSubmited(true);
  };

  return (
    <Modal
      open={visible}
      centered
      title={
        !isSubmited ? (
          <Title className={`${TitleMd} ${FontBold}`}>Đặt lại mật khẩu</Title>
        ) : (
          <Title className={`${TitleMd} ${FontBold}`}>Kiểm tra email</Title>
        )
      }
      onCancel={onCancel}
      footer={
        <Footer>
          {!isSubmited ? (
            <OkButton
              className={`${FontMedium}`}
              key='submit'
              type='submit' // Add this attribute
              form='forgot-password-form' // Link to the form's ID
            >
              Đặt lại mật khẩu
            </OkButton>
          ) : null}
        </Footer>
      }
    >
      {!isSubmited ? (
        <>
          <form id='forgot-password-form' onSubmit={handleSubmit(onSubmit)}>
            <NoticeText>{t('login.noticeForgot')}</NoticeText>
            <Body className=''>
              <RRFieldInput
                id='email'
                control={control}
                label={t('login.email')}
                className='w-full'
                placeholder={t('login.emailPlaceholder')}
                prefixIcon={images.Icon.MailClose}
                errors={errors}
              />
            </Body>
          </form>
        </>
      ) : (
        <>
          <LogoSection>
            <MainLogo src={images.Login.IlluEmail} alt='Icon' />
          </LogoSection>
          <NoticeText>
            <Trans
              i18nKey='login.notice'
              values={{ email }}
              components={{ strong: <strong /> }}
            />
          </NoticeText>
        </>
      )}
    </Modal>
  );
};

export default ForgotPassModal;

const Body = tw.div`py-5`;
const Title = tw.div``;
const Footer = tw.div`flex gap-3`;
const OkButton = tw.button`w-full rounded-lg bg-brand-300 py-2 text-black-1000`;
const NoticeText = tw.span`text-grey-600 inline-block`;
const LogoSection = tw.div`mb-10 flex flex-col items-center`;
const MainLogo = tw.img`mb-4`;
