import Cookies from 'js-cookie';
import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';

import { convertToUserPayload } from './adapters';
import { login, loginSuccess, logout, logoutAll, logoutSuccess } from './slice';

function* loginSaga(action: ReturnType<typeof login>): SagaIterator {
  const { data, callback } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'post',
    route: `/users/sign_in`,
    data: convertToUserPayload(data),
  });

  if (response.success) {
    const data = response.response?.data?.status;
    const user = data?.data?.user ?? {};
    const shouldChange = data?.data?.password_require_change;

    const token = data?.data?.token ?? '';

    if (shouldChange && !!callback) {
      callback();
    }

    yield put(loginSuccess({ token, user }));
  }
}

function* loginOutSaga(): SagaIterator {
  const response: ApiResponse = yield call(callApi, {
    method: 'delete',
    route: `/users/sign_out`,
  });

  Cookies.remove('token');
  if (response.success) {
    yield put(logoutSuccess());
  } else {
    yield put(logoutSuccess());
  }
}

function* logoutAllSaga(action: ReturnType<typeof logoutAll>): SagaIterator {
  const { id } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'post',
    route: `/users/${id}/sign_out_all_sessions`,
  });

  if (response.success) {
    yield put(logoutSuccess());
  }
}

export function* watchLoginSaga(): SagaIterator {
  yield takeEvery(login.type, loginSaga);
  yield takeEvery(logout.type, loginOutSaga);
  yield takeEvery(logoutAll.type, logoutAllSaga);
}
