import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ForgotPasswordForm } from '.';

interface LoginState {
  loading: boolean;
}

const initialState: LoginState = {
  loading: false,
};

const slice = createSlice({
  name: 'forgotPassword',
  initialState: initialState,
  reducers: {
    forgotPassword(state, _: PayloadAction<{ data: ForgotPasswordForm }>) {
      state.loading = true;
    },
    forgotPasswordSuccess(state) {
      state.loading = false;
    },
  },
});

export const { forgotPassword, forgotPasswordSuccess } = slice.actions;

export default slice.reducer;
