import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';

import { convertToUserPayload } from './adapters';
import { forgotPassword, forgotPasswordSuccess } from './slice';

function* forgotPasswordSaga(
  action: ReturnType<typeof forgotPassword>,
): SagaIterator {
  const { data } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'post',
    route: `/users/password`,
    data: convertToUserPayload(data),
  });

  if (response.success) {
    const data = response.response.data;
    yield put(forgotPasswordSuccess(data));
  }
}

export function* watchForgotPasswordSaga(): SagaIterator {
  yield takeEvery(forgotPassword.type, forgotPasswordSaga);
}
