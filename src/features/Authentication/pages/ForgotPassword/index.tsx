import { useState } from 'react';

import { useForm } from 'react-hook-form';
import { Trans, useTranslation } from 'react-i18next';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { UnauthenticatedRouteMap } from 'features/Core/App/components/UnauthenticatedRoute/constants';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import { RRFieldInput } from 'components';

import { forgotPassword } from './slice';

export interface ForgotPasswordForm {
  email: string;
}

interface ForgotPasswordProps {}

const ForgotPassword = ({}: ForgotPasswordProps) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<ForgotPasswordForm>();
  const email = watch('email');
  const [isSubmited, setIsSubmited] = useState(false);

  const onSubmit = (data: ForgotPasswordForm) => {
    dispatch(forgotPassword({ data }));
    setIsSubmited(true);
  };

  return (
    <Container>
      <WelComeSection>
        <WelCome src={images.Login.SignInWelcome} alt='Navio Login' />
      </WelComeSection>
      <MainFormContainer>
        {!isSubmited ? (
          <Form onSubmit={handleSubmit(onSubmit)}>
            <LogoSection>
              <MainLogo src={images.Login.Logo} alt='Icon' />
            </LogoSection>
            <NoticeText>{t('login.noticeForgot')}</NoticeText>
            <FieldInputContainer>
              <RRFieldInput
                id='email'
                control={control}
                label={t('login.email')}
                placeholder={t('login.emailPlaceholder')}
                prefixIcon={images.Icon.MailClose}
                errors={errors}
              />
            </FieldInputContainer>
            <SubmitButton className={`${ButtonSm} ${FontMedium}`} type='submit'>
              {t('login.forgotPasswordButton')}
            </SubmitButton>
          </Form>
        ) : (
          <Form className='max-w-sm'>
            <LogoSection>
              <MainLogo src={images.Login.IlluEmail} alt='Icon' />
            </LogoSection>
            <NoticeText>
              <Trans
                i18nKey='login.notice'
                values={{ email }}
                components={{ strong: <strong /> }}
              />
            </NoticeText>
            <SubmitButton
              className={`${ButtonSm} ${FontMedium}`}
              onClick={() => {
                navigate(UnauthenticatedRouteMap.SignIn);
              }}
            >
              {t('login.backToLogin')}
            </SubmitButton>
          </Form>
        )}
      </MainFormContainer>
    </Container>
  );
};

export default ForgotPassword;

const Container = tw.div`flex h-screen`;
const WelComeSection = tw.div`flex w-1/2 flex-wrap items-center justify-center`;
const WelCome = tw.img`h-screen max-w-full`;
const NoticeText = tw.span`text-grey-600 mb-10 inline-block`;
const MainFormContainer = tw.div`flex w-1/2 items-center justify-center`;
const Form = tw.form`w-full max-w-xs`;
const LogoSection = tw.div`mb-10 flex flex-col items-center`;
const MainLogo = tw.img`mb-4`;
const FieldInputContainer = tw.div`relative mb-3 w-full mb-10`;
const SubmitButton = tw.button`w-[360px] rounded-lg bg-brand-300 py-2 text-black-1000`;
