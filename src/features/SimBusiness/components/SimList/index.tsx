import React, { useCallback, useRef } from 'react';

import VirtualList from 'rc-virtual-list';

import type { SimType } from 'types/sim';

import RRScrollView from 'components/RRScrollView/RRScrollView';
import Spinner from 'components/Spinner';

import SimCard from './SimCard';

interface Props {
  loading?: boolean;
  data: SimType[];
  selectedSim?: SimType;
  onSelectedSim?: (sim: SimType) => void;
  fetchMore?: () => void;
}

const SimList = (props: Props) => {
  const { loading, data, selectedSim, onSelectedSim, fetchMore } = props || {};
  const scrollRef = useRef(null);

  const onScroll = (e: React.UIEvent<HTMLElement, UIEvent>) => {
    if (scrollRef.current) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const height = (scrollRef.current as any).getClientHeight();
      if (
        Math.abs(e.currentTarget.scrollHeight - e.currentTarget.scrollTop) -
          height <=
        10
      ) {
        fetchMore?.();
      }
    }
  };

  const handleSimSelectClick = useCallback(
    (sim: SimType) => {
      onSelectedSim?.(sim);
    },
    [onSelectedSim],
  );

  return (
    <RRScrollView
      className='w-[calc(100%+16px)]'
      onScroll={onScroll}
      ref={scrollRef}
    >
      <div className='w-[calc(100%-16px)]'>
        <VirtualList
          data={data}
          itemHeight={47}
          itemKey='email'
          onScroll={onScroll}
        >
          {(item, index) => {
            return (
              <SimCard
                key={index}
                data={item}
                checked={selectedSim?.sim_number === item.sim_number}
                onSelect={handleSimSelectClick}
              />
            );
          }}
        </VirtualList>
        {loading && <Spinner />}
      </div>
    </RRScrollView>
  );
};

export default SimList;
