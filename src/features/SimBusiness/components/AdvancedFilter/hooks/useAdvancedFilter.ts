import { useCallback, useEffect, useState } from 'react';

import { Dayjs } from 'dayjs';

import { FilterState, UseAdvancedFilterProps } from '../../../types';

export const useAdvancedFilter = ({
  onFilterSelect,
}: UseAdvancedFilterProps) => {
  const [filterState, setFilterState] = useState<FilterState>({
    servicePackage: [],
    typeOfSim: [],
    inventory: null,
    activeDateRange: { start: undefined, end: undefined },
    expiredDateRange: { start: undefined, end: undefined },
    isActive: false,
    lastedUpdated: null,
  });

  // Helper function to update filter state
  const updateFilterState = useCallback(
    (updates: Partial<FilterState>, isUpdated: boolean) => {
      setFilterState(prev => ({
        ...prev,
        ...updates,
        isActive: true,
        lastedUpdated: isUpdated ? Date.now() : prev.lastedUpdated,
      }));
    },
    [],
  );

  // Specific update functions
  const updateServicePackage = useCallback(
    (value: string[]) => {
      updateFilterState({ servicePackage: value }, true);
    },
    [updateFilterState],
  );

  const updateTypeOfSim = useCallback(
    (value: string[]) => {
      updateFilterState({ typeOfSim: value }, true);
    },
    [updateFilterState],
  );

  const updateInventory = useCallback(
    (value: boolean) => {
      updateFilterState({ inventory: value }, true);
    },
    [updateFilterState],
  );

  const updateActiveDateRange = useCallback(
    (type?: 'start' | 'end', date?: Dayjs) => {
      if (!date || !type) return;
      const newActiveDate = {
        ...filterState.activeDateRange,
        [type]: date,
      };

      const isUpdated = !!(newActiveDate.start && newActiveDate.end);
      updateFilterState(
        {
          activeDateRange: newActiveDate,
        },
        isUpdated,
      );
    },
    [filterState],
  );

  const updateExpiredDateRange = useCallback(
    (type?: 'start' | 'end', date?: Dayjs) => {
      if (!date || !type) return;
      const newExpiredDate = {
        ...filterState.expiredDateRange,
        [type]: date,
      };
      const isUpdated = !!(newExpiredDate.start && newExpiredDate.end);
      updateFilterState(
        {
          expiredDateRange: newExpiredDate,
        },
        isUpdated,
      );
    },
    [filterState],
  );

  // Reset function
  const reset = useCallback(() => {
    setFilterState({
      servicePackage: [],
      typeOfSim: [],
      inventory: null,
      activeDateRange: { start: undefined, end: undefined },
      expiredDateRange: { start: undefined, end: undefined },
      isActive: false,
      lastedUpdated: null,
    });
  }, []);

  // Effect to call onFilterSelect when state changes
  useEffect(() => {
    onFilterSelect?.(filterState);
  }, [filterState, onFilterSelect]);

  return {
    filterState,
    updateServicePackage,
    updateTypeOfSim,
    updateInventory,
    updateActiveDateRange,
    updateExpiredDateRange,
    reset,
  };
};
