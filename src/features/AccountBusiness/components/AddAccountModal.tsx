import React, { useCallback, useEffect, useMemo } from 'react';

import { Modal } from 'antd';
import { useProfile } from 'hooks';
import { useRole } from 'hooks/useRole';
import { yupResolver } from 'hooks/useYupResolver';
import { FormProvider, useForm } from 'react-hook-form';
import tw from 'tailwind-styled-components';

import { useAccountMutations } from 'features/AccountBusiness/hooks';
import { Account } from 'features/Profile/types';
import { t } from 'i18next';

import images from 'assets/images';
import { Icon, RRFieldInput, RRFieldPassword, RRFieldSelect } from 'components';
import { PrimaryButton } from 'components/Button';

import {
  createAccountDistributorSchema,
  createAccountSchema,
} from '../schema/accountSchemas';
import { Grid } from './AccountForm';

const DEFAULT_VALUE = {
  role_id: undefined,
  active_area: 'Vietnam',
  password: '123456',
  password_confirmation: '123456',
  username: '',
  full_name: '',
  phone_no: '',
  email: '',
  address: '',
  country: 'Vietnam',
};

interface AddAccountModalProps {
  defaultValues?: Account;
  visible: boolean;
  modalProps?: any;
  onClose: () => void;
  refreshExpandParentAccount?: (parentId: string) => void;
}

const AddAccountModal: React.FC<AddAccountModalProps> = ({
  defaultValues = DEFAULT_VALUE,
  visible,
  modalProps,
  onClose,
  refreshExpandParentAccount,
}) => {
  const { roles = [] } = useRole();
  const { profile } = useProfile();
  const { createAccount } = useAccountMutations();

  const methods = useForm({
    resolver: profile?.is_distributor
      ? yupResolver(createAccountDistributorSchema)
      : yupResolver(createAccountSchema),
    defaultValues: defaultValues,
    mode: 'onBlur',
  });

  const rolesOptions = useMemo(() => {
    if (modalProps?.is_end_user) return [];

    if (modalProps?.level === 1) {
      return roles
        .filter(item => item.role_type !== 'distributor')
        .map(role => ({
          value: role.id,
          label: role.name,
        }));
    }
    return roles.map(role => ({
      value: role.id,
      label: role.name,
    }));
  }, [roles, modalProps]);

  const { handleSubmit, formState, control } = methods || {};
  const { errors } = formState;

  useEffect(() => {
    if (!visible) {
      methods.reset(defaultValues);
    }

    return () => {
      methods.reset(defaultValues);
    };
  }, [defaultValues, visible, methods]);

  const handleCreateAccount = useCallback(
    data => {
      if (!formState.isValid) return;

      if (profile?.is_distributor) {
        data.password_confirmation = data.password;
      }
      data.parent_id = modalProps?.id;
      createAccount.mutate(data, {
        onSuccess: res => {
          onClose();
          const parentId = modalProps?.id || res?.parent_info?.id;
          refreshExpandParentAccount?.(parentId);
        },
        onError: error => {
          console.error('Create account error:', error);
        },
      });
    },
    [
      profile,
      createAccount,
      onClose,
      formState,
      methods,
      refreshExpandParentAccount,
    ],
  );

  const ModalTitle = useMemo(() => {
    return (
      <div className='flex items-center gap-3'>
        <Title>{t('accountForm.createTitle')}</Title>
        <div className='flex items-center gap-2'>
          {modalProps?.level === 0 && <Icon src={images.Icon.Level0Star} />}
          {modalProps?.level === 1 && <Icon src={images.Icon.Level1Star} />}
          <p className='text-sm font-semibold leading-[24px] text-grey-600'>
            {modalProps?.full_name}
          </p>
        </div>
      </div>
    );
  }, [modalProps]);

  return (
    <Modal
      centered
      title={ModalTitle}
      open={visible}
      onCancel={onClose}
      onOk={onClose}
      footer={null}
      width={920}
      closeIcon={
        <Icon className='size-8 rounded-full  p-1' src={images.Icon.X} />
      }
    >
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(handleCreateAccount)}>
          <GridFormWrapper>
            <Grid className='grid-cols-2'>
              <div
                className={
                  !profile?.is_distributor ? 'col-span-2' : 'col-span-1'
                }
              >
                <RRFieldInput
                  id='username'
                  required
                  label={t('accountForm.account')}
                  control={control}
                  placeholder={t('accountForm.account')}
                  className='w-full'
                  prefixIcon={images.Icon.User}
                  errors={errors}
                />
              </div>
              <RRFieldPassword
                id='password'
                required
                label={t('accountForm.password')}
                control={control}
                placeholder={t('accountForm.password')}
                className='w-full'
                prefixIcon={images.Icon.Lock}
                errors={errors}
              />
              {!profile?.is_distributor && (
                <RRFieldPassword
                  required
                  id='password_confirmation'
                  label={t('accountForm.confirmPassword')}
                  control={control}
                  placeholder={t('accountForm.confirmPassword')}
                  className='w-full'
                  prefixIcon={images.Icon.Lock}
                  errors={errors}
                />
              )}

              <RRFieldSelect
                id='role_id'
                required
                control={control}
                label={t('accountForm.role')}
                placeholder='Vui lòng chọn vai trò'
                className='w-full'
                options={rolesOptions ?? []}
                errors={errors}
              />
              <RRFieldInput
                id='full_name'
                required
                label={t('accountForm.fullName')}
                control={control}
                placeholder={t('accountForm.enterFullName')}
                className='w-full'
                errors={errors}
                prefixIcon={images.Icon.User}
              />
              <RRFieldInput
                id='phone_no'
                required
                label={t('accountForm.phoneNumber')}
                control={control}
                placeholder={t('accountForm.enterPhoneNumber')}
                className='w-full'
                errors={errors}
                prefixIcon={images.Icon.PhoneCall}
              />
              <RRFieldInput
                id='email'
                required
                label={t('accountForm.email')}
                control={control}
                placeholder={t('accountForm.enterEmail')}
                className='w-full'
                errors={errors}
                prefixIcon={images.Icon.MailClose}
              />
              <RRFieldInput
                id='address'
                required
                label={t('accountForm.address')}
                control={control}
                placeholder={t('accountForm.enterAddress')}
                className='w-full'
                errors={errors}
                prefixIcon={images.Icon.PinLocation}
              />
              <RRFieldInput
                id='country'
                label={t('accountForm.operatingArea')}
                disabled
                control={control}
                placeholder={t('accountForm.vietnam')}
                className='w-full'
                prefixIcon={images.Icon.VNFlag}
              />
            </Grid>
            <Grid className='grid-cols-1 pt-6'>
              <PrimaryButton
                htmlType='submit'
                disabled={createAccount.isPending || !formState.isValid}
                loading={createAccount.isPending}
              >
                {t('accountForm.createButton')}
              </PrimaryButton>
            </Grid>
          </GridFormWrapper>
        </form>
      </FormProvider>
    </Modal>
  );
};
const Title = tw.span`text-xl leading-[28px]`;
const GridFormWrapper = tw.div`h-full rounded-2xl pt-3 grid-cols-2`;

export default AddAccountModal;
