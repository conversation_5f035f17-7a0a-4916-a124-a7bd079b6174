import React, { useCallback } from 'react';

import { Button } from 'antd';
import { useProfile, useRole } from 'hooks';
import tw from 'tailwind-styled-components';

import AccountLogModal from 'features/History/Component/AccountLogModal';
import { t } from 'i18next';
import { addIsRoleType } from 'utils/role';

import images from 'assets/images';
import { Icon } from 'components';
import AccountBreadCrumb from 'components/AccountBreadcrumb';
import { LinkButton } from 'components/Button';
import { CollapseItemData } from 'components/RRCollapseTable';
import RRScrollView from 'components/RRScrollView/RRScrollView';

import { BreadCrumbItem } from '../types';
import AccountForm from './AccountForm';
import HorizontalDevice from './HorizontalDevice';
import MoveAccountModal from './MoveAccountModal';
import ResetPasswordModal from './ResetPassword';

interface AccountDetailsProps {
  mutations: any;
  modals: Record<string, boolean>;
  selectedAccount: CollapseItemData;
  breadcrumb: BreadCrumbItem[];
  onDeselectAccount: () => void;
  onToggleModal: (modalType: string, isOpen?: boolean) => void;
  refreshExpandParentAccount: (parentId: string) => void;
  updateState: (data: any) => void;
  onChangeBreadcrumb: (item: CollapseItemData) => void;
}

const AccountDetails: React.FC<AccountDetailsProps> = ({
  mutations,
  modals,
  selectedAccount,
  breadcrumb,
  onDeselectAccount,
  onToggleModal,
  updateState,
  refreshExpandParentAccount,
  onChangeBreadcrumb,
}) => {
  const { roles } = useRole();
  const { profile } = useProfile();

  const handleOpenModal = (modalType: string) => () => {
    onToggleModal(modalType, true);
  };
  const handleCloseModal = (modalType: string) => () => {
    onToggleModal(modalType, false);
  };

  const handleUpdateAccount = useCallback(
    (data: any) => {
      if (!data?.id) return;

      mutations.updateAccount.mutate(data, {
        onSuccess: res => {
          const parentId =
            res?.user?.parent_info?.id || selectedAccount.parent_info?.id || '';
          refreshExpandParentAccount(parentId);
          // update selectedAccount
          updateState({
            selectedAccount: {
              ...selectedAccount,
              ...(res.user || {}),
              ...addIsRoleType(res.user, roles),
            },
          });
        },
      });
    },
    [
      mutations.updateAccount,
      selectedAccount,
      roles,
      refreshExpandParentAccount,
    ],
  );

  const handleResetPassword = useCallback(
    (payload: { id: string; password: string }) => {
      if (!payload?.id) return;

      mutations.updateAccount.mutate(payload, {
        onSuccess: () => {
          handleCloseModal('resetPassword')();
        },
      });
    },
    [mutations.updateAccount, selectedAccount],
  );

  const handleSimpleTransferAccount = useCallback(
    (userIds: string[], parentId: string) => {
      mutations.transferAccount.mutate(
        { userIds, parentId },
        {
          onSuccess: res => {
            onChangeBreadcrumb(res?.user || {});
            handleCloseModal('simpleTransferModal')();
            refreshExpandParentAccount(parentId);
          },
        },
      );
    },
    [
      mutations.transferAccount,
      handleCloseModal,
      onChangeBreadcrumb,
      refreshExpandParentAccount,
    ],
  );

  return (
    <>
      <Interactions>
        <div />
        <LinkButton
          onClick={handleOpenModal('accountLog')}
          size='small'
          iconPosition='left'
          className='h-6'
          icon={<Icon src={images.Icon.Calendar} />}
        >
          {t('business.accountLog')}
        </LinkButton>
      </Interactions>
      <RRScrollView>
        <AccountFormContainer>
          <BreadcrumbContainer>
            <AccountBreadCrumb breadcrumb={breadcrumb} />
            {selectedAccount &&
              selectedAccount?.id?.toString() !== profile?.id?.toString() && (
                <Button
                  shape='circle'
                  icon={<Icon src={images.Icon.Trash} />}
                  onClick={handleOpenModal('confirmRemove')}
                  className='border-none bg-red-10 shadow-none hover:bg-red-alpha20'
                />
              )}
          </BreadcrumbContainer>
          <AccountForm
            roles={roles}
            isUpdating={mutations.updateAccount.isPending}
            isDeleting={mutations.deleteAccount.isPending}
            selectedAccount={selectedAccount}
            onUpdateAccount={handleUpdateAccount}
            onOpenModal={handleOpenModal}
            onOpenSimpleTransferModal={handleOpenModal('simpleTransferModal')}
          />
        </AccountFormContainer>
        <HorizontalDevice accountId={selectedAccount?.id} />
      </RRScrollView>
      <AccountLogModal
        accountId={selectedAccount?.id}
        visible={modals.accountLog}
        onClose={handleCloseModal('accountLog')}
      />
      <ResetPasswordModal
        breadcrumb={breadcrumb}
        isDeleting={mutations.updateAccount.isPending}
        selectedAccount={selectedAccount}
        visible={modals.resetPassword}
        onSubmit={handleResetPassword}
        onClose={handleCloseModal('resetPassword')}
      />
      <MoveAccountModal
        multipleAccountSelected={[selectedAccount]}
        visible={modals.simpleTransferModal}
        onSubmit={handleSimpleTransferAccount}
        onClose={handleCloseModal('simpleTransferModal')}
        onSelectedMultipleAccount={onDeselectAccount}
      />
    </>
  );
};

const Interactions = tw.div`flex justify-between items-center mb-4`;
const AccountFormContainer = tw.div`border-grey-100 w-full rounded-xl border mb-4`;
const BreadcrumbContainer = tw.div`border-b border-grey-100 py-4 px-6 flex items-center justify-between gap-3 items-center h-[55px]`;

export default AccountDetails;
