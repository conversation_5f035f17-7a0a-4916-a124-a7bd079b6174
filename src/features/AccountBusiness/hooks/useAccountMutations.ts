import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useDispatch } from 'react-redux';

import { logout } from 'features/Authentication/pages/Login/slice';
import { t } from 'i18next';
import { createApiMutationFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';

import showToast from 'components/RRToastMessage/Toast';

import {
  CreateAccountData,
  ExpandAccountResponse,
  TransferAccountsData,
  UpdateAccountData,
} from '../types';

interface DeleteAccountData {
  id: string;
  parentId?: string;
}

// Type definitions

export const useAccountMutations = (profile?: any) => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();

  const invalidateAccounts = () => {
    queryClient.invalidateQueries({
      queryKey: queryKeys.accounts.all,
    });
  };

  const expandAccount = useMutation<
    ExpandAccountResponse,
    Error,
    {
      id: string;
      page?: number;
    }
  >({
    mutationFn: createApiMutationFn<
      ExpandAccountResponse,
      { id: string; page?: number }
    >(({ id, page = 1 }) => ({
      method: 'GET',
      route: `/users/children?user_id=${id}&page=${page}&per_page=20`,
    })),
    onSuccess: data => {
      return data;
    },
  });

  const createAccount = useMutation<any, Error, CreateAccountData>({
    mutationFn: createApiMutationFn<any, CreateAccountData>(
      (data: CreateAccountData) => ({
        method: 'POST',
        route: '/users',
        data,
      }),
    ),
    onSuccess: () => {
      showToast('success', t('accountForm.createInfoSuccess'));
    },
  });

  const updateAccount = useMutation<any, Error, UpdateAccountData>({
    mutationFn: createApiMutationFn<any, UpdateAccountData>(
      (data: UpdateAccountData) => ({
        method: 'PUT',
        route: `/users/${data.id}`,
        data,
      }),
    ),
    onSuccess: (res, data) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.detail(data.id?.toString()),
      });
      showToast('success', t('common.updateSuccess'));
    },
  });

  const transferAccount = useMutation<any, Error, TransferAccountsData>({
    mutationFn: createApiMutationFn<any, TransferAccountsData>(
      ({ userIds, parentId }: TransferAccountsData) => ({
        method: 'PUT',
        route: `/users/${userIds[0]}/transfer`,
        data: {
          new_parent_id: parentId,
        },
      }),
    ),
    onSuccess: (data, { userIds }) => {
      invalidateAccounts();
      queryClient.invalidateQueries({
        queryKey: queryKeys.accounts.detail(userIds[0]),
      });
      showToast('success', t('accountForm.moveAccountSuccess'));
    },
  });

  const transferMultipleAccount = useMutation<any, Error, TransferAccountsData>(
    {
      mutationFn: createApiMutationFn<any, TransferAccountsData>(
        ({ userIds, parentId }: TransferAccountsData) => ({
          method: 'PUT',
          route: `/users/batch_transfer`,
          data: {
            user_ids: userIds,
            new_parent_id: parentId,
          },
        }),
      ),
      onSuccess: res => {
        invalidateAccounts();
        showToast(
          'success',
          res?.message || t('accountForm.moveAccountSuccess'),
        );
      },
    },
  );

  const deleteAccount = useMutation<any, Error, DeleteAccountData>({
    mutationFn: createApiMutationFn<any, DeleteAccountData>(({ id }) => ({
      method: 'DELETE',
      route: `/users/${id}`,
    })),
    onSuccess: (response, { id: deletedAccountId }) => {
      showToast('success', 'Account deleted successfully');
      if (deletedAccountId === profile?.id?.toString()) {
        dispatch(logout());
      }
    },
  });

  return {
    expandAccount,
    createAccount,
    updateAccount,
    deleteAccount,
    transferAccount,
    transferMultipleAccount,
  };
};
