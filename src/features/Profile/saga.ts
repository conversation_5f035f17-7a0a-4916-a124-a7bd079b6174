import { SagaIterator } from 'redux-saga';
import { call, put, takeLatest } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';

import {
  getProfile,
  getProfileSuccess,
  updatePassword,
  updateProfile,
  uploadAvatar,
  uploadAvatarSuccess,
} from './slice';

function* updateProfileSaga(
  action: ReturnType<typeof updateProfile>,
): SagaIterator {
  const { data, callback } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'put',
    route: `/users/${data.role_id}`,
    data: data,
  });

  if (response.success) {
    callback(true);
  }
}

function* getProfileSaga(action: ReturnType<typeof getProfile>): SagaIterator {
  const { id } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: `/users/${id}`,
  });

  if (response.success) {
    yield put(getProfileSuccess({ profile: response.response.data.user }));
  }
}

function* updatePasswordSaga(
  action: ReturnType<typeof updatePassword>,
): SagaIterator {
  const { data, callback, id } = action.payload;
  const response: ApiResponse = yield call(callApi, {
    method: 'put',
    route: `/users/${id}`,
    data: data,
  });

  if (response.success) {
    callback(true);
  }
}

function* uploadAvatarSaga(
  action: ReturnType<typeof uploadAvatar>,
): SagaIterator {
  const { id, file, callback } = action.payload;

  const formData = new FormData();
  formData.append('avatar', file);

  const response: ApiResponse = yield call(callApi, {
    method: 'post',
    route: `/users/${id}/upload_avatar`,
    data: formData,
    headersConfig: { 'Content-Type': 'multipart/form-data' },
  });

  if (response.success) {
    yield put(uploadAvatarSuccess());
    callback(true);
  } else {
    callback(false);
  }
}

export function* watchProfileSaga(): SagaIterator {
  yield takeLatest(updateProfile.type, updateProfileSaga);
  yield takeLatest(getProfile.type, getProfileSaga);
  yield takeLatest(updatePassword.type, updatePasswordSaga);
  yield takeLatest(uploadAvatar.type, uploadAvatarSaga);
}
