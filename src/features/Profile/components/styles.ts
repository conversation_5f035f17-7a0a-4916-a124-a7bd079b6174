import { Form } from 'antd';
import tw from 'tailwind-styled-components';

export const FormContainer = tw.form`flex flex-1`;
export const FormSingleItem = tw(Form.Item)``;
export const GridFormWarpper = tw.div`flex flex-1 flex-col rounded-2xl border border-grey-100 px-6 py-1`;
export const Grid = tw.div`grid gap-1`;
export const SubmitButton = tw.button`rounded-lg bg-brand-300 my-3 py-2 text-black-1000 font-medium leading-[24px]`;

export const GridFormWarpperSmall = tw.div`flex-wrap flex-col rounded-2xl border border-grey-100 p-6`;
export const Label = tw.div`mb-2`;
