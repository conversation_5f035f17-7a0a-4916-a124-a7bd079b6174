import { useEffect, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import { t } from 'i18next';
import { callApi } from 'services/api/api';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import { RRFieldSelect } from 'components';
import RRFieldInput from 'components/FormField/RRFieldInput';
import RRCreditCard from 'components/RRCreditCard';
import showToast from 'components/RRToastMessage/Toast';

import {
  FormContainer,
  Grid,
  GridFormWarpper,
  GridFormWarpperSmall,
  SubmitButton,
} from './styles';

const DriverInfoForm: React.FC<{ className?: string }> = ({ className }) => {
  const [options, setOptions] = useState<{ label: string; value: string }[]>(
    [],
  );
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [selectedDriverInfo, setSelectedDriverInfo] = useState<any>(null);

  const methods = useForm({
    defaultValues: {
      device: options[0],
      code: '',
      fullName: '',
      phoneNumber: '',
      driverLicense: '',
      queryInput: '',
    },
  });

  const { handleSubmit, formState, reset, watch, control } = methods;
  const selectedDevice = watch('device');
  const name = watch('fullName');
  const cardNo = watch('code');
  const rcardId = watch('driverLicense');

  // const cardId = useMemo(() => {
  //   return rcardId.replace(/(.{4})/g, '$1-').slice(0, -1); // Add dashes and remove trailing dash
  // }, [rcardId]);
  const { errors } = formState;

  const onSubmit = async data => {
    try {
      const response = await callApi({
        method: 'put',
        route: `/devices/${selectedDevice}/driver`,
        data: {
          driver_name: data.fullName,
          phone_number: data.phoneNumber,
          driver_license: data.driverLicense,
          driver_tag: data.code,
          rfid_command: data.queryInput,
        },
      });

      if (response.success) {
        showToast('success', t('Cập nhật thông tin và ghi thẻ thành công'));
      } else {
        showToast('failed', t('Cập nhật thông tin và ghi thẻ thất bại'));
        console.error('Failed to update driver info:', response.error);
      }
    } catch (error) {
      showToast('failed', t('profile.updateDriverError'));
      console.error('Error updating driver info:', error);
    }
  };

  useEffect(() => {
    const fetchOptions = async () => {
      try {
        const response = await callApi({
          method: 'get',
          route: '/device-selectors',
        });

        if (response.success) {
          const fetchedOptions = response.response.data.device_infos.map(
            item => ({
              label: item.display_name,
              value: item.device_imei,
            }),
          );
          setOptions(fetchedOptions);
        } else {
          console.error('Failed to fetch options:', response.error);
        }
      } catch (error) {
        console.error('Error fetching options:', error);
      }
    };

    fetchOptions();
  }, []);

  useEffect(() => {
    if (selectedDriverInfo?.driver) {
      reset({
        ...methods.getValues(),
        fullName: selectedDriverInfo.driver.driver_name || '',
        phoneNumber: selectedDriverInfo.driver.phone_number || '',
        driverLicense: selectedDriverInfo.driver.driver_license || '',
        code: selectedDriverInfo.driver.driver_tag || '',
        queryInput: selectedDriverInfo.driver.rfid_command || '',
      });
    }
  }, [selectedDriverInfo, reset]);

  useEffect(() => {
    if (options.length === 1) {
      reset({
        ...methods.getValues(),
        device: options[0],
      });
      handleDeviceSelect(options[0].value);
    }
  }, [options]);

  const handleDeviceSelect = async (selectedValue: string) => {
    try {
      const response = await callApi({
        method: 'get',
        route: `/devices/${selectedValue}/driver`,
      });

      if (response.success) {
        setSelectedDriverInfo(response.response.data);
      } else {
        console.error('Failed to fetch driver info:', response.error);
      }
    } catch (error) {
      console.error('Error fetching driver info:', error);
    }
  };

  return (
    <FormProvider {...methods}>
      <FormContainer
        className={`${className} gap-6`}
        onSubmit={handleSubmit(onSubmit)}
      >
        <GridFormWarpper>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldSelect
              id='device'
              control={control}
              label={t('deviceDetail.deviceType')}
              className='mb-3 w-full'
              options={options}
              errors={errors}
            />
            <RRFieldInput
              id='code'
              control={control}
              label='Mã thẻ'
              placeholder='Nhập mã thẻ'
              defaultValue={methods.getValues('code')}
              className='mb-3 w-full'
              prefixIcon={images.Icon.CodeSquare}
              errors={errors}
            />
          </Grid>
        </GridFormWarpper>
        <GridFormWarpperSmall>
          <RRCreditCard
            cardNumber={cardNo}
            cardHolderName={name}
            cardId={rcardId}
          />
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='fullName'
              control={control}
              label={t('accountForm.fullName')}
              placeholder={t('accountForm.enterFullName')}
              className='mb-3 w-full'
              errors={errors}
              prefixIcon={images.Icon.User}
              defaultValue={methods.getValues('fullName')}
            />

            <RRFieldInput
              id='driverLicense'
              control={control}
              label={t('profile.driverLicenseLabel')}
              placeholder={t('profile.driverLicensePlaceholder')}
              className='mb-3 w-full'
              prefixIcon={images.Icon.UserSquare}
              defaultValue={methods.getValues('driverLicense')}
            />
            <RRFieldInput
              id='phoneNumber'
              control={control}
              label={t('accountForm.phoneNumber')}
              placeholder={t('accountForm.enterPhoneNumber')}
              className='mb-3 w-full'
              errors={errors}
              prefixIcon={images.Icon.PhoneCall}
              defaultValue={methods.getValues('phoneNumber')}
            />
            <RRFieldInput
              id='queryInput'
              control={control}
              label={t('profile.queryInputLabel')}
              placeholder={t('accountForm.enterPhoneNumber')}
              className='mb-3 w-full'
              errors={errors}
              defaultValue={methods.getValues('queryInput')}
            />
          </Grid>
          <SubmitButton
            className={`w-full ${ButtonSm} ${FontMedium}`}
            onClick={() => {
              showToast('success', t('profile.updateCardSuccess'));
            }}
          >
            {t('profile.writeCardButton')}
          </SubmitButton>
        </GridFormWarpperSmall>
      </FormContainer>
    </FormProvider>
  );
};

export default DriverInfoForm;
