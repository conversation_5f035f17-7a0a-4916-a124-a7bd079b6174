import { useCallback } from 'react';

import Cookies from 'js-cookie';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';

import { t } from 'i18next';

import images from 'assets/images';
import { ButtonSm, FontMedium } from 'assets/styles';
import RRFieldPassword from 'components/FormField/RRFieldPassword';
import showToast from 'components/RRToastMessage/Toast';

import { updatePassword } from '../slice';
import { FormContainer, Grid, GridFormWarpper, SubmitButton } from './styles';

const UpdatePasswordForm: React.FC<{ className?: string }> = ({
  className,
}) => {
  const id = Cookies.get('user_id');

  const dispatch = useDispatch();
  const methods = useForm({
    defaultValues: {
      current_password: '',
      password: '',
      password_confirmation: '',
    },
  });

  const { handleSubmit, control, formState } = methods;
  const { errors } = formState;

  const onSubmit = useCallback(
    data => {
      const callback = (success: boolean) => {
        if (success) {
          showToast('success', t('profile.updatePasswordSucess'));
        }
      };
      dispatch(updatePassword({ id: id ?? 0, data, callback }));
    },
    [id, dispatch],
  );

  return (
    <FormProvider {...methods}>
      <FormContainer className={className} onSubmit={handleSubmit(onSubmit)}>
        <GridFormWarpper>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldPassword
              control={control}
              id='current_password'
              label={t('profile.currentPassword')}
              placeholder='Nhập mật khẩu hiện tại'
              className='mb-3 w-full'
              prefixIcon={images.Icon.Lock}
              errors={errors}
            />
            <RRFieldPassword
              control={control}
              id='password'
              label={t('profile.newPassword')}
              placeholder='Nhập mật khẩu mới'
              className='mb-3 w-full'
              prefixIcon={images.Icon.Lock}
              errors={errors}
            />
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldPassword
              control={control}
              id='password_confirmation'
              label={t('profile.confirmPassword')}
              placeholder='Nhập lại mật khẩu mới'
              className='mb-3 w-full'
              prefixIcon={images.Icon.Lock}
              errors={errors}
            />
          </Grid>

          <SubmitButton className={`${ButtonSm} ${FontMedium}`} type='submit'>
            {t('profile.updatePasswordButton')}
          </SubmitButton>
        </GridFormWarpper>
      </FormContainer>
    </FormProvider>
  );
};
export default UpdatePasswordForm;
