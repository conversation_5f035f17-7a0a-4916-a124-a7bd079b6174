import { useCallback, useEffect, useState } from 'react';

import { Avatar, Flex, Upload, UploadProps } from 'antd';
import { useProfile } from 'hooks/useProfile';
import Cookies from 'js-cookie';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyMdExtend,
  ButtonSm,
  FontMedium,
  FontSemibold,
} from 'assets/styles';
import { Icon } from 'components';
import RRFieldInput from 'components/FormField/RRFieldInput';
import showToast from 'components/RRToastMessage/Toast';

import { updateProfile, uploadAvatar } from '../slice';
import { FormContainer, Grid, GridFormWarpper, SubmitButton } from './styles';

const ProfileForm: React.FC<{ className?: string }> = ({ className }) => {
  const dispatch = useDispatch();
  const { profile } = useProfile();
  const id = Cookies.get('user_id');
  const [avatar, setAvatar] = useState<string | undefined>(
    profile?.avatar_url
      ? `https://core.navio.asia/${profile?.avatar_url}`
      : images.Icon.BellNotification,
  );

  const methods = useForm({
    defaultValues: {
      username: '',
      role_id: 0,
      full_name: '',
      phone_no: '',
      email: '',
      address: '',
      active_area: 'Vietnam',
    },
  });

  const { handleSubmit, control, formState, reset } = methods;
  const { errors } = formState;

  useEffect(() => {
    if (profile) {
      reset({
        username: profile?.username ?? '',
        role_id: profile.role_id ?? 0,
        full_name: profile.full_name ?? '',
        phone_no: profile.phone_no ?? '',
        email: profile.email ?? '',
        address: profile.address ?? '',
        active_area: profile.active_area ?? '',
      });
      setAvatar(
        profile.avatar_url
          ? `https://core.navio.asia/${profile.avatar_url}`
          : images.Icon.BellNotification,
      );
    }
  }, [profile, reset]);

  const onSubmit = useCallback(
    data => {
      const { account, ...rest } = data;
      if (id) {
        rest.role_id = +id;
      }
      const callback = (success: boolean) => {
        if (success) {
          showToast('success', t('profile.updateInfoSuccess'));
        }
      };
      dispatch(updateProfile({ data: rest, callback }));
    },
    [dispatch, id],
  );

  const handleChangeFile: UploadProps['onChange'] = ({ file }) => {
    if (file && !!profile?.id) {
      const callback = (success: boolean) => {
        if (success) {
          showToast('success', t('profile.avatarUploadSuccess'));
        } else {
          showToast('failed', t('profile.avatarUploadError'));
        }
      };

      dispatch(
        uploadAvatar({
          id: profile?.id,
          file: file as any,
          callback,
        }),
      );
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    setAvatar(URL.createObjectURL(file as any));
  };

  return (
    <FormProvider {...methods}>
      <FormContainer className={className} onSubmit={handleSubmit(onSubmit)}>
        <GridFormWarpper>
          <Grid className='my-3 grid-cols-1 flex-col'>
            <EmailLabel className='text-sm font-medium text-grey-400'>
              Ảnh đại diện
            </EmailLabel>

            <Flex align='center'>
              <Avatar className='mr-6 bg-brand-100' size={64} src={avatar} />
              <Flex vertical>
                <EmailField>
                  Tối thiểu 800x800 được khuyến nghị
                  <br />
                  Định dạng JPG or PNG,GIF
                </EmailField>
                <ButtonUpload type='button'>
                  <Upload
                    beforeUpload={() => false}
                    onChange={handleChangeFile}
                    showUploadList={false}
                  >
                    Tải lên ảnh mới
                  </Upload>
                </ButtonUpload>
              </Flex>
            </Flex>
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <Flex flex={1} justify='space-between' align='center'>
              <EmailLabel className={`${BodyMdExtend} ${FontSemibold}`}>
                {t('profile.email')}
              </EmailLabel>
              <Flex align='center'>
                <EmailField>{profile?.email}</EmailField>
                <Icon src={images.Icon.ChevronRight} />
              </Flex>
            </Flex>
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='username'
              label={t('accountForm.account')}
              control={control}
              disabled
              placeholder={t('accountForm.account')}
              className='mb-3 w-full'
              prefixIcon={images.Icon.User}
              defaultValue={methods.getValues('username')}
            />
            <RRFieldInput
              id='fullName'
              name='full_name'
              label={t('accountForm.fullName')}
              control={control}
              placeholder={t('accountForm.enterFullName')}
              className='mb-3 w-full'
              errors={errors}
              prefixIcon={images.Icon.User}
              defaultValue={methods.getValues('full_name')}
            />
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='phoneNumber'
              name='phone_no'
              label={t('accountForm.phoneNumber')}
              control={control}
              placeholder={t('accountForm.enterPhoneNumber')}
              className='mb-3 w-full'
              errors={errors}
              prefixIcon={images.Icon.PhoneCall}
              defaultValue={methods.getValues('phone_no')}
            />
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='address'
              name='address'
              label={t('accountForm.address')}
              control={control}
              placeholder={t('accountForm.enterAddress')}
              className='mb-3 w-full'
              errors={errors}
              prefixIcon={images.Icon.PinLocation}
              defaultValue={methods.getValues('address')}
            />
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='active_area'
              label={t('accountForm.operatingArea')}
              control={control}
              disabled
              placeholder={t('accountForm.vietnam')}
              className='mb-3 w-full'
              prefixIcon={images.Icon.VNFlag}
            />
          </Grid>

          <SubmitButton className={`${ButtonSm} ${FontMedium}`} type='submit'>
            {t('profile.saveInfoButton')}
          </SubmitButton>
        </GridFormWarpper>
      </FormContainer>
    </FormProvider>
  );
};

const EmailField = tw.div`text-sm text-grey-600 leading-[24px] overflow-hidden text-ellipsis whitespace-nowrap`;
const EmailLabel = tw.div`overflow-hidden text-ellipsis whitespace-nowrap`;
const ButtonUpload = tw.button`flex justify-center flex-row border-grey-100 border-[1.5px] border-solid box-border text-sm font-mendium w-2/3 rounded-lg px-3 py-1 mt-2`;

export default ProfileForm;
