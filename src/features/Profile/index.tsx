import { useEffect, useState } from 'react';

import { Tabs } from 'antd';
import { useProfile } from 'hooks/useProfile';
import { useLocation, useNavigate } from 'react-router-dom';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import { BodyLg, H4 } from 'assets/styles';

import DriverInfoForm from './components/DriverInfoForm';
import ProfileForm from './components/ProfileForm';
import UpdatePasswordForm from './components/UpdatePasswordForm';

const { TabPane } = Tabs;
interface ProfileProps {}

const Profile = ({}: ProfileProps) => {
  const { profile } = useProfile();
  const location = useLocation();
  const navigate = useNavigate();
  const [activeKey, setActiveKey] = useState('info');

  const handleTabChange = (key: string) => {
    setActiveKey(key);
    const queryParams = new URLSearchParams(location.search);
    queryParams.set('active', key);
    navigate({ search: queryParams.toString() });
  };

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const active = queryParams.get('active');
    if (active) {
      setActiveKey(active);
    }
  }, [location.search]);

  return (
    <Container>
      <Header>
        <OverviewSection>
          <GreetingText className={BodyLg}>
            Xin chào {profile?.full_name}
          </GreetingText>
          <OverviewText className={H4}>Thông tin cá nhân</OverviewText>
        </OverviewSection>
      </Header>
      <MainContainer>
        <>
          <DrawerBodyWrapper>
            <Tabs
              activeKey={activeKey}
              className='flex size-full flex-1'
              style={{ height: '100%' }}
              tabPosition='left'
              onChange={handleTabChange}
            >
              <TabPane tab={t('profile.individualInfoTab')} key='info'>
                <ProfileForm className='w-2/3' />
              </TabPane>
              <TabPane tab={t('profile.changePasswordTab')} key='password'>
                <UpdatePasswordForm className='w-2/3' />
              </TabPane>
              <TabPane tab={t('profile.driverInfoTab')} key='driver'>
                <DriverInfoForm />
              </TabPane>
            </Tabs>
          </DrawerBodyWrapper>
        </>
      </MainContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col p-6`;
const MainContainer = tw.div`flex flex-1 py-6`;
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
export const DrawerBodyWrapper = tw.div`flex h-full flex-1`;
export const DrawerHeaderWrapper = tw.div`flex items-center justify-between`;
export const HeaderTitleInteraction = tw.div`flex gap-14`;
export const HeaderTitle = tw.div``;
export const Interactions = tw.div`flex items-center gap-7`;
export const Action = tw.div`flex items-center gap-2 cursor-pointer`;

export default Profile;
