import { PayloadAction, createSlice } from '@reduxjs/toolkit';

import { Account, AccountRole } from './types';

interface ProfileState {
  profile: Account;
  loading: boolean;
  error: string | null;
  roles: AccountRole[];
}

const initialState: ProfileState = {
  profile: {
    username: '',
    name: '',
    active_area: '',
    address: '',
    email: '',
    full_name: '',
    phone_no: '',
    role_id: 0,
    role_type: '',
  },
  roles: [],
  loading: false,
  error: null,
};

const slice = createSlice({
  name: 'profile',
  initialState: initialState,
  reducers: {
    updateProfile(
      state,
      _: PayloadAction<{ data: Account; callback: (success: boolean) => void }>,
    ) {
      state.loading = true;
    },
    updateProfileSuccess(state) {
      state.loading = false;
    },
    getProfile(
      state,
      _: PayloadAction<{
        id: string | number;
      }>,
    ) {
      state.loading = true;
    },
    getProfileSuccess(state, action: PayloadAction<{ profile: Account }>) {
      state.loading = false;
      state.profile = action.payload.profile;
    },
    updatePassword(
      state,
      _: PayloadAction<{
        id: string | number;
        data: {
          newPassword: string;
          confirmPassword: string;
        };
        callback: (success: boolean) => void;
      }>,
    ) {
      state.loading = true;
    },
    uploadAvatar(
      state,
      _: PayloadAction<{
        id: string | number;
        file: File;
        callback: (success: boolean) => void;
      }>,
    ) {
      state.loading = true;
    },
    uploadAvatarSuccess(state) {
      state.loading = false;
    },
    updateRoles(state, action: PayloadAction<{ roles: AccountRole[] }>) {
      state.roles = action.payload.roles;
    },
  },
});

export const {
  updateProfile,
  getProfile,
  getProfileSuccess,
  updatePassword,
  uploadAvatar,
  uploadAvatarSuccess,
  updateRoles,
} = slice.actions;

export default slice.reducer;
