import type { UserPermission } from 'types/UserRoleTypes';

export interface Account {
  name: string;
  id?: number;
  username: string;
  active_area: string;
  address: string;
  email: string;
  full_name: string;
  phone_no: string;
  role_id?: number;
  password?: string;
  password_confirmation?: string;
  avatar_url?: string;
  active?: boolean;
  created_at?: string;
  role_type?: string;
  is_distributor?: boolean;
  is_end_user?: boolean;
  permissions?: UserPermission[];
  parent_info?: any;
}

export interface AccountRole {
  description: string;
  id: number;
  name: string;
  role_type: string;
}

export interface Role {
  id: number;
  name: string;
  role: string;
  permissions: string[];
}
