import { AbilityBuilder, createMongoAbility } from '@casl/ability';
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from 'store/rootReducer';

export const appPermissionsSelector = (state: RootState) =>
  state.appPermissions.permissions;

export const selectAppPermissions = createSelector(
  [appPermissionsSelector],
  appPermissions => {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    if (appPermissions) {
      appPermissions.forEach(action => {
        can(action.toString(), 'all');
      });
    }

    return build();
  },
);
