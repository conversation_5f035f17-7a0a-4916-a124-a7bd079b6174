import { useEffect, useMemo } from 'react';

import { Badge } from 'antd';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';

import AccountBusiness from 'features/AccountBusiness';
import Contact from 'features/Contact';
import DeviceBusiness from 'features/DeviceBusiness';
import History from 'features/History';
import Map from 'features/Map';
import NotificationDevice from 'features/Notification';
import { selectTotalUnread } from 'features/Notification/selectors';
import { fetchNotifications } from 'features/Notification/slice';
import Overview from 'features/Overview';
import { Account } from 'features/Profile/types';
import Report from 'features/Report';
import ReportIssue from 'features/ReportIssue';
import SimBusiness from 'features/SimBusiness';
import { TFunction } from 'i18next';
import type { RootState } from 'store/rootReducer';

import {
  ClockWiseIcon,
  HomeIcon,
  MapIcon,
  NotificationIcon,
  ReportIcon,
  SettingCircleIcon,
  SupportIcon,
  WarningIcon,
} from 'assets/icons';
import { Colors } from 'assets/styles';
import PageWithSidebar from 'components/PageWithSidebar';
import { MultipleSidebarMenuItems } from 'components/PageWithSidebar/types';
import SwitchRoute, { PageRoute } from 'components/SwitchRoute';

import { selectAppPermissions } from '../../selectors';
import { AbilityContext } from '../../utils/permissions';

const AuthenticatedRoute = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const permissions = useSelector(selectAppPermissions);
  const unread = useSelector(selectTotalUnread);

  const profile = useSelector(
    (state: RootState) => state.login.profile,
  ) as Account;

  const menu = useMemo(() => {
    return getMenuItems(t, unread, profile);
  }, [t, unread, profile]);

  const authenticatedRoute: PageRoute[] = [
    {
      path: '/*',
      component: () => <PageWithSidebar menu={menu} />,
    },
  ];

  useEffect(() => {
    dispatch(fetchNotifications({ page: 1, isRead: false }));
  }, [dispatch]);

  return (
    <AbilityContext.Provider value={permissions}>
      <SwitchRoute routes={authenticatedRoute} />
    </AbilityContext.Provider>
  );
};

export default AuthenticatedRoute;

const getMenuItems = (
  t: TFunction,
  unread: number,
  profile: Account,
): MultipleSidebarMenuItems[] => {
  const isDistributor = profile ? profile?.role_type === 'distributor' : true;
  return [
    {
      label: t('sidebar.controller'),
      menu: [
        {
          icon: HomeIcon,
          label: t('sidebar.overview'),
          route: '/overview',
          exact: true,
          component: Overview,
        },
        {
          icon: MapIcon,
          label: t('sidebar.map'),
          route: '/map',
          component: Map,
        },
        ...(isDistributor
          ? [
              {
                icon: SettingCircleIcon,
                label: t('sidebar.business'),
                route: '/business',
                subItems: [
                  {
                    label: 'Quản lý tài khoản',
                    route: '/business/accounts',
                    component: AccountBusiness,
                  },
                  {
                    label: 'Quản lý thiết bị',
                    route: '/business/devices',
                    component: DeviceBusiness,
                  },
                  {
                    label: 'Kho Sim',
                    route: '/business/sim',
                    component: SimBusiness,
                  },
                ],
              },
            ]
          : []),
        {
          icon: ReportIcon,
          label: t('sidebar.report'),
          route: '/report',
          component: Report,
        },
      ],
    },
    { label: '---' },
    {
      label: t('sidebar.setting'),

      menu: [
        {
          icon: NotificationIcon,
          label: t('sidebar.noti'),
          route: '/notification',
          component: NotificationDevice,
          rightElement: (
            <Badge
              count={unread}
              styles={{
                indicator: {
                  borderColor: Colors.red[200],
                  backgroundColor: Colors.red[200],
                },
              }}
            />
          ),
        },
        {
          icon: ClockWiseIcon,
          label: t('sidebar.history'),
          route: '/history',
          component: History,
        },
        {
          icon: WarningIcon,
          label: t('sidebar.reportIssue'),
          route: '/issue-report',
          component: ReportIssue,
        },
        {
          icon: SupportIcon,
          label: t('sidebar.contact'),
          route: '/support',
          component: Contact,
        },
      ],
    },
  ];
};
