import React from 'react';
import SwitchRoute, { PageRoute } from 'components/SwitchRoute';
import Login from 'features/Authentication/pages/Login';
import ForgotPassword from 'features/Authentication/pages/ForgotPassword';
import { UnauthenticatedRouteMap } from './constants';

const UnauthenticatedRoute = () => {
  const unauthenticatedRoute: PageRoute[] = [
    {
      path: UnauthenticatedRouteMap.SignIn,
      component: Login,
      exact: true,
    },
    {
      path: UnauthenticatedRouteMap.ForgotPassword,
      component: ForgotPassword,
    },
  ];

  return <SwitchRoute routes={unauthenticatedRoute} defaultRoute="/login" />;
};

export default UnauthenticatedRoute;
