import { useCallback, useEffect, useMemo, useState } from 'react';

import { onMessage } from 'firebase/messaging';
import Cookies from 'js-cookie';
import { isNil } from 'lodash';
import { useDispatch, useSelector } from 'react-redux';

// import { fetchPermissions } from './slice';
import { selectLoginData } from 'features/Authentication/pages/Login/selectors';
import { newNotification } from 'features/Notification/slice';
import { getMessagingToken, messaging } from 'services/firebase';

import AuthenticatedRoute from './components/AuthenticatedRoute';
import UnauthenticatedRoute from './components/UnauthenticatedRoute';

const App = () => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const dispatch = useDispatch();

  const token = useSelector(selectLoginData);
  const cookiesToken = Cookies.get('token');
  const localToken = useMemo(() => {
    return cookiesToken || token;
  }, [token, cookiesToken]);

  const [isAuthenticated, setIsAuthenticated] = useState<boolean>();
  const checkCookies = useCallback(() => {
    if (localToken) {
      setIsAuthenticated(true);
    } else {
      setIsAuthenticated(false);
    }
  }, [localToken]);

  useEffect(() => {
    checkCookies();
  }, [token]);

  // useEffect(() => {
  //   dispatch(fetchPermissions());
  // }, [isAuthenticated]);

  useEffect(() => {
    getMessagingToken();
  }, []);

  useEffect(() => {
    const listenForMessages = () => {
      console.log('Listening for messages...');
      onMessage(messaging, payload => {
        dispatch(
          newNotification({
            id: payload.messageId,
            content: payload.notification?.title ?? '',
          }),
        );
      });
    };
    listenForMessages();
  });

  if (isNil(isAuthenticated)) return null;

  return (
    <>{isAuthenticated ? <AuthenticatedRoute /> : <UnauthenticatedRoute />}</>
  );
};

export default App;
