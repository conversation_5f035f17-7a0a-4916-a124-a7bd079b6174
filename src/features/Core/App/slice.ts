import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserPermission } from 'types/UserRoleTypes';

interface PermissionState {
  loading: boolean;
  permissions: UserPermission[];
}

const initialState: PermissionState = {
  loading: false,
  permissions: [],
};

const slice = createSlice({
  name: 'appPermissions',
  initialState: initialState,
  reducers: {
    fetchPermissions(state) {
      state.loading = true;
    },
    fetchPermissionsSuccess(
      state,
      action: PayloadAction<{ permissions: UserPermission[] }>,
    ) {
      state.loading = false;
      state.permissions = action.payload.permissions;
    },
  },
});

export const { fetchPermissions, fetchPermissionsSuccess } = slice.actions;

export default slice.reducer;
