import { SagaIterator } from 'redux-saga';
import { call, put, takeEvery } from 'redux-saga/effects';

import { callApi } from 'services/api/api';
import { ApiResponse } from 'services/api/types';
import { UserPermission } from 'types/UserRoleTypes';

import { fetchPermissions, fetchPermissionsSuccess } from './slice';

function* fetchPermissionsSaga(
  _action: ReturnType<typeof fetchPermissions>,
): SagaIterator {
  const response: ApiResponse = yield call(callApi, {
    method: 'get',
    route: `/permissions`,
  });

  if (response.success) {
    const payload = {
      permissions: [
        UserPermission.ADMIN,
        UserPermission.USER,
        UserPermission.CAN_FILTER,
      ],
    };

    yield put(fetchPermissionsSuccess(payload));
  }
}

export function* watchPermissionSaga(): SagaIterator {
  yield takeEvery(fetchPermissions.type, fetchPermissionsSaga);
}
