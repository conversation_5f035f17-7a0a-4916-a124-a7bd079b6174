import { call, put, takeEvery } from 'redux-saga/effects';
import { SagaIterator } from 'redux-saga';
import {
  submitFeedback,
  submitFeedbackSuccess,
  submitFeedbackFailure,
  FeedbackFormData,
} from './slice';

const GOOGLE_SCRIPT_URL =
  'https://script.google.com/macros/s/AKfycbxmxQVq4S95YrvCVL5qjQrs3s2XPJF9V7znot-nOiEGyOiAtYa-3zqZoLW5eToQl_KdDw/exec';

function* submitFeedbackSaga(
  action: ReturnType<typeof submitFeedback>,
): SagaIterator {
  const { fullName, email, phone, message } = action.payload;

  try {
    // Create form data for URL encoding
    const formData = new URLSearchParams();
    formData.append('fullName', fullName);
    formData.append('email', email);
    formData.append('phone', phone);
    formData.append('message', message);

    const response = yield call(fetch, GOOGLE_SCRIPT_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    if (response.ok) {
      console.log('Feedback submitted successfully');
      yield put(submitFeedbackSuccess());
    } else {
      const errorText = yield call([response, 'text']);
      yield put(
        submitFeedbackFailure(`Failed to submit feedback: ${errorText}`),
      );
    }
  } catch (error) {
    console.error('Error submitting feedback:', error);
    yield put(
      submitFeedbackFailure(
        error instanceof Error ? error.message : 'An unknown error occurred',
      ),
    );
  }
}

export function* watchContactSaga(): SagaIterator {
  yield takeEvery(submitFeedback.type, submitFeedbackSaga);
}
