import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface FeedbackFormData {
  fullName: string;
  email: string;
  phone: string;
  message: string;
}

interface ContactState {
  submitting: boolean;
  submitSuccess: boolean;
  submitError: string | null;
}

const initialState: ContactState = {
  submitting: false,
  submitSuccess: false,
  submitError: null,
};

const contactSlice = createSlice({
  name: 'contact',
  initialState,
  reducers: {
    submitFeedback(state, _: PayloadAction<FeedbackFormData>) {
      state.submitting = true;
      state.submitSuccess = false;
      state.submitError = null;
    },
    submitFeedbackSuccess(state) {
      state.submitting = false;
      state.submitSuccess = true;
      state.submitError = null;
    },
    submitFeedbackFailure(state, action: PayloadAction<string>) {
      state.submitting = false;
      state.submitSuccess = false;
      state.submitError = action.payload;
    },
    resetSubmitState(state) {
      state.submitting = false;
      state.submitSuccess = false;
      state.submitError = null;
    },
  },
});

export const {
  submitFeedback,
  submitFeedbackSuccess,
  submitFeedbackFailure,
  resetSubmitState,
} = contactSlice.actions;

export default contactSlice.reducer;
