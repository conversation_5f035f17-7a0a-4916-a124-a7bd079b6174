import { Flex } from 'antd';
import { useProfile } from 'hooks/useProfile';
import tw from 'tailwind-styled-components';

import { t } from 'i18next';

import images from 'assets/images';
import { BodyLg, H4 } from 'assets/styles';
import { Icon, RRStaticMap } from 'components';
import { PinType } from 'components/RRStaticMap';

import FeadbackForm from './components/FeadbackForm';

interface ContactProps {}

const Contact = ({}: ContactProps) => {
  const { profile } = useProfile();

  return (
    <Container>
      <Header>
        <OverviewSection>
          <GreetingText className={BodyLg}>
            Xin chào {profile?.full_name}
          </GreetingText>
          <OverviewText className={H4}>{t('contact.headerText')}</OverviewText>
        </OverviewSection>
      </Header>
      <MainContainer>
        <LeftContainer>
          <RRStaticMap
            showLabel={false}
            label=''
            pinType={PinType.Location}
            position={{ lat: 16.0688, lng: 108.2215 }}
            disabledScroll={true}
          />
          <CompanyInfoContainer>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.Home} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.companyName')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  Công ty TNHH Ứng Dụng Công Nghệ Navio
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.MapLocation} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.address')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  RR16-RR17 Hồng Lĩnh, Phường 15, Quận 10, Thành phố Hồ Chí Minh
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.PhoneCall} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.phone')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  84-901-290-290
                </Subtitle>
              </TextContainer>
            </RowInfo>
            <RowInfo className={`${LineBox}`}>
              <IconInfo src={images.Icon.MailClose} />
              <TextContainer>
                <Title className={`${SingleLineText}`}>
                  {t('contact.email')}
                </Title>
                <Subtitle className={`${SingleLineText}`}>
                  <EMAIL>
                </Subtitle>
              </TextContainer>
            </RowInfo>
          </CompanyInfoContainer>
        </LeftContainer>
        <RightContainer>
          <FeadbackForm className='my-6 ml-10' />
        </RightContainer>
      </MainContainer>
    </Container>
  );
};

const Container = tw.div`flex h-full flex-col p-6`;

const RightContainer = tw.div`flex flex-1`;
const LeftContainer = tw.div`flex flex-col w-[55%] py-6`;
const MainContainer = tw.div`flex flex-1`;
const SingleLineText = 'line-clamp-1';
const Header = tw.div`flex justify-between`;
const OverviewSection = tw.div`flex flex-col`;
const GreetingText = tw.div`text-grey-600`;
const OverviewText = tw.h1`text-4xl font-bold`;
const CompanyInfoContainer = tw(Flex)`flex flex-col my-2`;
const RowInfo = tw.div`flex flex-1 flex-row gap-4 py-3 text-sm`;
const LineBox = 'border-b-[0.5px] border-grey-100';
const IconInfo = tw(Icon)`mt-1`;
const TextContainer = tw.div`flex flex-1 flex-col gap-1`;

const Title = tw.span`
  font-medium
  text-black-1000
  leading-[24px]
  font-semibold
`;

const Subtitle = tw.span`
  text-grey-600
  leading-[20px]
`;

export default Contact;
