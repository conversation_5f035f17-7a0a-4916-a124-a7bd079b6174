import { useEffect } from 'react';

import TextArea from 'antd/lib/input/TextArea';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';

import { t } from 'i18next';

import images from 'assets/images';
import {
  BodyMdExtend,
  ButtonSm,
  FontMedium,
  FontSemibold,
} from 'assets/styles';
import RRFieldInput from 'components/FormField/RRFieldInput';
import showToast from 'components/RRToastMessage/Toast';

import {
  selectContactSubmitError,
  selectContactSubmitSuccess,
  selectContactSubmitting,
} from '../selectors';
import { FeedbackFormData, resetSubmitState, submitFeedback } from '../slice';
import {
  FormContainer,
  Grid,
  GridFormWarpper,
  Label,
  SubmitButton,
} from './styles';

const FeadbackForm: React.FC<{ className?: string }> = ({ className }) => {
  const dispatch = useDispatch();
  const isSubmitting = useSelector(selectContactSubmitting);
  const submitSuccess = useSelector(selectContactSubmitSuccess);
  const submitError = useSelector(selectContactSubmitError);

  const methods = useForm<FeedbackFormData>({
    defaultValues: {
      fullName: '',
      email: '',
      phone: '',
      message: '',
    },
  });

  const { handleSubmit, formState, reset, control } = methods;
  const { errors } = formState;

  useEffect(() => {
    if (submitSuccess) {
      showToast('success', t('contact.submitSuccess', 'Thông tin đã được gửi'));
      reset();
      setTimeout(() => {
        dispatch(resetSubmitState());
      }, 3000);
    }
  }, [submitSuccess, reset, dispatch]);

  useEffect(() => {
    if (submitError) {
      showToast('failed', t('contact.submitError', 'Gửi thất bại!'));
      setTimeout(() => {
        dispatch(resetSubmitState());
      }, 3000);
    }
  }, [submitError, dispatch]);

  const onSubmit = (data: FeedbackFormData) => {
    dispatch(submitFeedback(data));
  };

  return (
    <FormProvider {...methods}>
      <FormContainer className={className} onSubmit={handleSubmit(onSubmit)}>
        <GridFormWarpper>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='fullName'
              control={control}
              label={t('contact.name')}
              placeholder={t('contact.placeholderName')}
              className='mb-3 w-full'
              errors={errors}
            />
            <RRFieldInput
              id='email'
              control={control}
              label={t('contact.email')}
              placeholder={t('contact.placeholderEmail')}
              className='mb-3 w-full'
              errors={errors}
            />
          </Grid>
          <Grid className='mt-3 grid-cols-1'>
            <RRFieldInput
              id='phone'
              control={control}
              label={t('contact.phone')}
              placeholder={t('contact.placeholderPhone')}
              className='mb-3 w-full'
              errors={errors}
            />
            <Label className={`${BodyMdExtend} ${FontSemibold}`}>
              {t('contact.message')}
            </Label>
            <TextArea
              value={methods.watch('message')}
              onChange={e => {
                methods.setValue('message', e.target.value);
              }}
              placeholder={t('contact.placeholderMessage')}
              className='w-full bg-grey-50'
              rows={6}
            />
            {errors.message && (
              <span className='text-red-500 text-sm'>
                {errors.message.message}
              </span>
            )}
          </Grid>

          <SubmitButton
            onClick={handleSubmit(onSubmit)}
            className={`${ButtonSm} ${FontMedium}`}
            icon={<img src={images.Icon.SendMessage} />}
            loading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting
              ? t('contact.submitting', 'Đang gửi...')
              : t('contact.sendFeadbackButton')}
          </SubmitButton>
        </GridFormWarpper>
      </FormContainer>
    </FormProvider>
  );
};
export default FeadbackForm;
