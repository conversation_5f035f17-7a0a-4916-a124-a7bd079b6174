import { all } from 'redux-saga/effects';

import { watchForgotPasswordSaga } from 'features/Authentication/pages/ForgotPassword/saga';
import { watchLoginSaga } from 'features/Authentication/pages/Login/saga';
import { watchContactSaga } from 'features/Contact/saga';
import { watchPermissionSaga } from 'features/Core/App/saga';
import { watchHistorySaga } from 'features/History/saga';
import { watchNotificationSaga } from 'features/Notification/saga';
import { watchOverviewSaga } from 'features/Overview/saga';
import { watchProfileSaga } from 'features/Profile/saga';

export default function* rootSaga() {
  yield all([
    watchNotificationSaga(),
    watchLoginSaga(),
    watchOverviewSaga(),
    watchPermissionSaga(),
    watchForgotPasswordSaga(),
    watchProfileSaga(),
    watchHistorySaga(),
    watchContactSaga(),
  ]);
}
