import { combineReducers } from '@reduxjs/toolkit';

import forgotPasswordReducer from 'features/Authentication/pages/ForgotPassword/slice';
import loginReducer from 'features/Authentication/pages/Login/slice';
import contactReducer from 'features/Contact/slice';
import AppPermissionsReducer from 'features/Core/App/slice';
import historyReducer from 'features/History/slice';
import notificationReducer from 'features/Notification/slice';
import overviewReducer from 'features/Overview/slice';
import profileReducer from 'features/Profile/slice';

const rootReducer = combineReducers({
  login: loginReducer,
  password: forgotPasswordReducer,
  overview: overviewReducer,
  profile: profileReducer,
  history: historyReducer,
  contact: contactReducer,
  notification: notificationReducer,
  appPermissions: AppPermissionsReducer,
});

export type RootState = ReturnType<typeof rootReducer>;
export default rootReducer;
