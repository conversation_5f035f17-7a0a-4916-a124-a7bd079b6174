import { useEffect } from 'react';

import { useQuery } from '@tanstack/react-query';
import { useDispatch, useSelector } from 'react-redux';

import { updateRoles } from 'features/Profile/slice';
import type { AccountRole } from 'features/Profile/types';
import { createApiQueryFn } from 'services/reactQuery';
import { queryKeys } from 'services/reactQuery/queryHelpers';
import { RootState } from 'store/rootReducer';

export interface UseRoleReturn {
  roles: AccountRole[];
  isLoading: boolean;
  isFetching: boolean;
  error: string | null;
  refetch: () => void;
}

export const useRole = (): UseRoleReturn => {
  const dispatch = useDispatch();
  const roles = useSelector((state: RootState) => state.profile.roles);

  // React Query
  const query = useQuery({
    queryKey: queryKeys.roles.all,
    queryFn: createApiQueryFn({ method: 'GET', route: '/roles' }),
    enabled: roles.length === 0,
  });

  // Sync to store with data transformation
  useEffect(() => {
    if (query.data) {
      const rolesData = Array.isArray(query.data)
        ? query.data
        : query.data.roles || [];

      // Transform Role[] to AccountRole[] if needed
      const transformedRoles = rolesData.map(role => ({
        id: role.id,
        name: role.name,
        description: role.description || '',
        role_type: role.role_type || role.role || '',
      }));

      dispatch(updateRoles({ roles: transformedRoles }));
    }
  }, [query.data, dispatch]);

  return {
    roles,
    isLoading: query.isLoading,
    error: query.error?.message || null,
    refetch: query.refetch,
    isFetching: query.isFetching,
  };
};

export default useRole;
