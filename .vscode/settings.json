{
  // Editor Settings
  "editor.renderWhitespace": "boundary",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSaveMode": "file",
  "editor.wordWrap": "bounded",
  "editor.wordWrapColumn": 80,
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.inlineSuggest.enabled": true,
  "editor.linkedEditing": true,
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 80,
  "editor.suggest.insertMode": "replace",
  "editor.suggestSelection": "first",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },

  // Code Actions
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "explicit",
    "source.removeUnusedImports": "explicit"
  },

  // TypeScript Settings
  "typescript.validate.enable": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.classMemberSnippets.enabled": false,
  "typescript.updateImportsOnFileMove.enabled": "always",
  "typescript.inlayHints.parameterNames.enabled": "literals",
  "typescript.inlayHints.variableTypes.enabled": false,

  // JavaScript Settings
  "javascript.validate.enable": true,
  "javascript.suggest.autoImports": true,
  "javascript.updateImportsOnFileMove.enabled": "always",

  // CSS/Styling Settings
  "css.lint.unknownAtRules": "ignore",
  "scss.lint.unknownAtRules": "ignore",
  "less.lint.unknownAtRules": "ignore",
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"],
    "tw`([^`]*)",
    "tw\"([^\"]*)",
    "tw='([^']*)',",
    "tw=\"([^\"]*)\")",
    "tw={([^}]*)}>",
    ["tw\\.[^`]+`([^`]*)`", "([a-zA-Z0-9\\-:]+)"]
  ],

  // Prettier Settings (consistent with .prettierrc)
  "prettier.singleQuote": true,
  "prettier.arrowParens": "avoid",
  "prettier.trailingComma": "all",
  "prettier.semi": true,
  "prettier.tabWidth": 2,
  "prettier.useTabs": false,
  "prettier.printWidth": 80,
  "prettier.jsxSingleQuote": true,
  "prettier.endOfLine": "lf",

  // ESLint Settings
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "eslint.alwaysShowStatus": true,

  // File Explorer Settings
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.patterns": {
    "*.ts": "${capture}.js",
    "*.tsx": "${capture}.js",
    "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
    "*.jsx": "${capture}.js",
    "*.vue": "${capture}.*.ts, ${capture}.*.js",
    "index.d.ts": "*.d.ts",
    "shims-vue.d.ts": "*.d.ts",
    "*.cpp": "${capture}.hpp, ${capture}.h, ${capture}.hxx",
    "*.cxx": "${capture}.hpp, ${capture}.h, ${capture}.hxx",
    "tsconfig.json": "tsconfig.*.json",
    "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml"
  },

  // Search Settings
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.next": true,
    "**/.nuxt": true,
    "**/coverage": true,
    "**/.nyc_output": true
  },

  // Files Settings
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.trimTrailingWhitespace": true,
  "files.associations": {
    "*.json": "jsonc"
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/build": true,
    "**/.git": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true
  },

  // Git Settings
  "git.enableCommitSigning": false,
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,

  // Emmet Settings
  "emmet.includeLanguages": {
    "typescript": "html",
    "typescriptreact": "html",
    "javascript": "html",
    "javascriptreact": "html"
  },
  "emmet.triggerExpansionOnTab": true,

  // Terminal Settings
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.fontFamily": "'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace",

  // Performance Settings
  "extensions.experimental.affinity": {
    "asvetliakov.vscode-neovim": 1
  },
  "cSpell.words": ["ccid", "imei"],
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}
