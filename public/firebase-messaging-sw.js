importScripts(
  'https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js',
);
importScripts(
  'https://www.gstatic.com/firebasejs/9.23.0/firebase-messaging-compat.js',
);

const firebaseConfig = {
  apiKey: 'AIzaSyAzradtdgHr23IQyXYdOatmPWuTfllUc4o',
  authDomain: 'navio-staging.firebaseapp.com',
  databaseURL:
    'https://navio-staging-default-rtdb.asia-southeast1.firebasedatabase.app',
  projectId: 'navio-staging',
  storageBucket: 'navio-staging.firebasestorage.app',
  messagingSenderId: '166400805714',
  appId: '1:166400805714:web:8e7e7b1b4e654eacc9488b',
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Retrieve Firebase Messaging instance
const messaging = firebase.messaging();

messaging.onBackgroundMessage(payload => {
  console.log(
    '[firebase-messaging-sw.js] Received background message:',
    payload,
  );

  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    // icon: payload.notification.icon,
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
